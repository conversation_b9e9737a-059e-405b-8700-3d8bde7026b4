{"name": "cerebro-idp", "version": "1.0.0", "type": "module", "description": "Identity Provider OIDC para ecosistema Cerebro", "scripts": {"dev": "nodemon --watch src --exec tsx src/index.ts", "build": "tsc -p tsconfig.json", "start": "node dist/src/index.js", "migrate": "tsx scripts/migrate.ts", "migrate:status": "tsx scripts/migrate.ts status", "migrate:rollback": "tsx scripts/migrate.ts rollback", "seed": "tsx scripts/seed.ts", "seed:clean": "tsx scripts/seed.ts clean", "seed:stats": "tsx scripts/seed.ts stats", "gen:jwk": "tsx scripts/gen-jwk.ts", "db:reset": "npm run migrate && npm run seed && npm run gen:jwk", "db:cleanup": "tsx scripts/cleanup.ts", "db:cleanup:force": "tsx scripts/cleanup.ts --force", "db:cleanup:stats": "tsx scripts/cleanup.ts stats", "db:cleanup:optimize": "tsx scripts/cleanup.ts optimize", "db:cleanup:full": "tsx scripts/cleanup.ts full", "health": "tsx scripts/health-check.ts", "health:json": "tsx scripts/health-check.ts json", "health:continuous": "tsx scripts/health-check.ts continuous", "health:database": "tsx scripts/health-check.ts database", "health:redis": "tsx scripts/health-check.ts redis", "health:oidc": "tsx scripts/health-check.ts oidc", "health:jwk": "tsx scripts/health-check.ts jwk", "dev:full": "npm run docker:up && sleep 5 && npm run db:reset && npm run dev", "docker:up": "docker compose -f docker-compose.dev.yml up -d", "docker:down": "docker compose -f docker-compose.dev.yml down", "docker:restart": "npm run docker:down && npm run docker:up", "docker:logs": "docker compose -f docker-compose.dev.yml logs -f", "test": "echo \"Tests not implemented yet\" && exit 1", "test:unit": "echo \"Unit tests not implemented yet\" && exit 1", "test:integration": "echo \"Integration tests not implemented yet\" && exit 1", "lint": "echo \"<PERSON><PERSON> not configured yet\" && exit 1", "lint:fix": "echo \"<PERSON><PERSON> not configured yet\" && exit 1", "type-check": "tsc --noEmit", "prod:build": "NODE_ENV=production npm run build", "prod:start": "NODE_ENV=production npm run start", "backup:db": "mkdir -p backups && docker compose -f docker-compose.dev.yml exec -T db mysqldump -u cerebro -pcerebro cerebro > backups/backup_$(date +%Y%m%d_%H%M%S).sql", "util:tree": "tree -a --prune -I 'node_modules|lib|dist|.git|.firebase|backup_*' > _docs/file-system.md", "postinstall": "npm run util:check-env || echo '⚠️ Remember to configure .env file'"}, "dependencies": {"argon2": "^0.44.0", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "ioredis": "^5.7.0", "jose": "^6.1.0", "mysql2": "^3.14.4", "pino": "^9.9.1", "pino-http": "^10.5.0", "zod": "^4.1.5"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.3.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "tsx": "^4.20.5", "typescript": "^5.9.2"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "keywords": ["oauth2", "oidc", "identity-provider", "authentication", "authorization", "jwt", "pkce", "express", "typescript", "mysql", "redis"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/tu-usuario/cerebro-idp#readme", "repository": {"type": "git", "url": "git+https://github.com/tu-usuario/cerebro-idp.git"}, "bugs": {"url": "https://github.com/tu-usuario/cerebro-idp/issues"}}