# Dockerfile para Cerebro IDP - Optimizado para producción

# ================================================
# Build Stage
# ================================================
FROM node:20-alpine AS builder

# Instalar dependencias de sistema necesarias para compilación
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git

# Crear directorio de trabajo
WORKDIR /app

# Copiar package files
COPY package*.json ./
COPY tsconfig.json ./

# Instalar dependencias (incluyendo devDependencies para build)
RUN npm ci --include=dev

# Copiar código fuente
COPY src/ ./src/
COPY scripts/ ./scripts/
COPY migrations/ ./migrations/

# Build de la aplicación
RUN npm run build

# Limpiar devDependencies
RUN npm ci --omit=dev && npm cache clean --force

# ================================================
# Production Stage
# ================================================
FROM node:20-alpine AS production

# Metadata
LABEL maintainer="<EMAIL>"
LABEL description="Cerebro Identity Provider - OIDC/OAuth2 Server"
LABEL version="1.0.0"

# Instalar dependencias de runtime
RUN apk add --no-cache \
    dumb-init \
    curl \
    tzdata

# Crear usuario no-root
RUN addgroup -g 1001 -S nodejs && \
    adduser -S cerebro -u 1001 -G nodejs

# Crear directorios necesarios
RUN mkdir -p /app/dist /app/logs /app/backups && \
    chown -R cerebro:nodejs /app

# Establecer directorio de trabajo
WORKDIR /app

# Cambiar a usuario no-root
USER cerebro

# Copiar aplicación compilada desde build stage
COPY --from=builder --chown=cerebro:nodejs /app/dist ./dist/
COPY --from=builder --chown=cerebro:nodejs /app/node_modules ./node_modules/
COPY --from=builder --chown=cerebro:nodejs /app/package*.json ./

# Copiar scripts y migraciones necesarios para runtime
COPY --chown=cerebro:nodejs migrations/ ./migrations/
COPY --chown=cerebro:nodejs scripts/ ./scripts/

# Variables de entorno por defecto
ENV NODE_ENV=production
ENV PORT=4000
ENV LOG_LEVEL=info

# Exponer puerto
EXPOSE 4000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${PORT}/health || exit 1

# Punto de entrada
ENTRYPOINT ["dumb-init", "--"]

# Comando por defecto
CMD ["node", "dist/index.js"]

# ================================================
# Development Stage (para docker-compose.dev.yml)
# ================================================
FROM node:20-alpine AS development

# Instalar dependencias de desarrollo
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl

# Crear usuario de desarrollo
RUN addgroup -g 1001 -S nodejs && \
    adduser -S cerebro -u 1001 -G nodejs

WORKDIR /app

# Cambiar a usuario de desarrollo
USER cerebro

# Variables de entorno para desarrollo
ENV NODE_ENV=development
ENV LOG_LEVEL=debug

# Instalar tsx globalmente para desarrollo
USER root
RUN npm install -g tsx nodemon
USER cerebro

# Exponer puerto
EXPOSE 4000

# Health check más frecuente en desarrollo
HEALTHCHECK --interval=10s --timeout=3s --start-period=2s --retries=2 \
    CMD curl -f http://localhost:${PORT}/health || exit 1

# Comando de desarrollo
CMD ["npm", "run", "dev"]
