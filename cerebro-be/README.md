# 🧠 Cerebro Identity Provider (IDP)

Proveedor de identidad OIDC completo con panel de administración, construido con Node.js, Express, MySQL/MariaDB y Redis.

## 🚀 Características

- **OIDC/OAuth2 Compliant**: Authorization Code Flow con PKCE
- **Panel de Administración**: Gestión completa de usuarios y clientes
- **Audit Logs**: Registro completo de actividad administrativa
- **JWK Rotation**: Rotación automática de claves de firma
- **Multi-tenancy**: Soporte para múltiples clientes OIDC
- **Rate Limiting**: Protección contra ataques de fuerza bruta
- **Health Checks**: Monitoreo completo del sistema
- **TypeScript**: Tipado fuerte en toda la aplicación

## 📋 Requisitos

- **Node.js** >= 20.0.0
- **npm** >= 10.0.0
- **Docker** (para desarrollo local)
- **Docker Compose** v2+

## ⚡ Quick Start

### 1. Clonar y configurar

```bash
git clone <repo-url>
cd cerebro-idp

# Instalar dependencias
npm install

# Configurar variables de entorno
make setup-env
```

### 2. Editar configuración

Edita el archivo `.env` generado con tus valores:

```bash
# Cambiar estas URLs según tu entorno
PUBLIC_BASE_URL=http://localhost:4000
OIDC_ISSUER=http://localhost:4000

# Cambiar password del admin
ADMIN_PWD=TuPasswordSegura123!

# Cambiar JWT secret en producción
JWT_SECRET=tu-jwt-secret-super-seguro-cambiar-en-produccion
```

### 3. Arrancar servicios y aplicación

```bash
# Opción A: Setup completo automático
make dev-full

# Opción B: Paso a paso
make docker-up      # Levantar MySQL + Redis
make db-setup       # Migraciones + seeds + JWK
make dev            # Arrancar en desarrollo
```

### 4. Verificar instalación

```bash
# Health check
make health

# Abrir en navegador
open http://localhost:4000/.well-known/openid-configuration
```

## 🛠️ Comandos Disponibles

### Desarrollo

```bash
make dev              # Ejecutar en modo desarrollo
make dev-full         # Setup completo + desarrollo
make build            # Compilar para producción
make start            # Ejecutar en producción
```

### Base de Datos

```bash
make db-setup         # Setup inicial completo
make db-reset         # Resetear DB completamente
make db-migrate       # Ejecutar migraciones
make db-seed          # Ejecutar seeds
make db-cleanup       # Limpiar datos expirados
```

### Docker

```bash
make docker-up        # Levantar servicios
make docker-down      # Parar servicios
make docker-restart   # Reiniciar servicios
make docker-logs      # Ver logs
```

### Monitoreo

```bash
make health           # Health check completo
make logs             # Ver logs de aplicación
make db-status        # Estado de base de datos
```

### Seguridad

```bash
make gen-jwk          # Generar nueva JWK
```

### Testing (TODO)

```bash
make test             # Ejecutar todos los tests
make test-unit        # Tests unitarios
make test-integration # Tests de integración
make lint             # Linter
make type-check       # Verificar tipos TS
```

### Utilidades

```bash
make shell-db         # Conectar a MySQL
make shell-redis      # Conectar a Redis
make backup-db        # Backup de base de datos
make clean            # Limpiar archivos generados
```

## 🏗️ Arquitectura

```
├── migrations/          # Scripts SQL de migración
├── scripts/            # Scripts de utilidades (health, cleanup, etc.)
├── src/
│   ├── crypto/         # Manejo de JWK y criptografía
│   ├── middlewares/    # Middlewares Express
│   ├── routes/         # Rutas de la API
│   └── services/       # Lógica de negocio
├── _docs/              # Documentación
└── docker-compose.dev.yml
```

## 🔧 Configuración

### Variables de Entorno Principales

| Variable | Descripción | Ejemplo |
|----------|-------------|---------|
| `NODE_ENV` | Entorno de ejecución | `development` |
| `PORT` | Puerto del servidor | `4000` |
| `PUBLIC_BASE_URL` | URL pública del servidor | `http://localhost:4000` |
| `OIDC_ISSUER` | URL del issuer OIDC | `http://localhost:4000` |
| `DB_*` | Configuración de MySQL | Ver `.env.example` |
| `REDIS_URL` | URL de Redis | `redis://127.0.0.1:6379` |
| `JWT_SECRET` | Secreto para JWT | ⚠️ **Cambiar en producción** |

### Estructura de Base de Datos

- `users` - Usuarios del sistema
- `roles` - Roles (ADMIN, CLIENT, PRO)
- `user_roles` - Relación usuarios-roles
- `oidc_clients` - Clientes OAuth2/OIDC
- `auth_codes` - Códigos de autorización
- `refresh_tokens` - Tokens de refresco
- `jwk_keys` - Claves de firma JWT
- `audit_logs` - Logs de auditoría

## 🔒 Endpoints Principales

### OIDC/OAuth2

```
GET  /.well-known/openid-configuration  # Configuración OIDC
GET  /jwks.json                         # Claves públicas
GET  /authorize                         # Autorización
POST /token                             # Intercambio de tokens
GET  /userinfo                          # Información del usuario
```

### Admin Panel

```
POST /admin/auth/login                  # Login admin
GET  /admin/users                       # Listar usuarios
POST /admin/users                       # Crear usuario
GET  /admin/clients                     # Listar clientes OIDC
POST /admin/clients                     # Crear cliente
GET  /admin/audit/logs                  # Logs de auditoría
```

## 🚀 Despliegue en Producción

### 1. Variables de entorno

```bash
# Cambiar estas variables críticas
NODE_ENV=production
JWT_SECRET=clave-ultra-segura-de-256-bits-minimo
ADMIN_PWD=password-admin-super-segura

# URLs de producción
PUBLIC_BASE_URL=https://auth.tu-dominio.com
OIDC_ISSUER=https://auth.tu-dominio.com

# Base de datos externa
DB_HOST=tu-db-host.com
DB_PASSWORD=password-db-segura
```

### 2. Build y Deploy

```bash
# Build
make prod-build

# Deploy (ejemplo con Docker)
docker build -t cerebro-idp .
docker run -d --env-file .env.prod cerebro-idp
```

### 3. Configuración SSL/TLS

- Usar proxy reverso (nginx, Cloudflare, etc.)
- Certificados SSL válidos
- HSTS headers habilitados

## 🔍 Troubleshooting

### Problemas Comunes

**Error: "Database connection failed"**
```bash
make docker-up        # Verificar que MySQL esté corriendo
make db-status       # Verificar estado de DB
```

**Error: "No active JWK found"**
```bash
make gen-jwk         # Generar nueva clave JWK
```

**Error: "Redis connection failed"**
```bash
make docker-restart  # Reiniciar servicios Docker
```

### Health Checks

```bash
# Check completo
make health

# Checks específicos
npm run health:database
npm run health:redis
npm run health:oidc
npm run health:jwk

# Monitoreo continuo
npm run health:continuous
```

### Logs

```bash
# Logs de aplicación
make logs

# Logs de Docker
make docker-logs

# Logs específicos
docker logs cerebro-db
docker logs cerebro-redis
```

## 🛡️ Seguridad

### Producción

- [ ] Cambiar `JWT_SECRET` por valor aleatorio de 256+ bits
- [ ] Usar contraseñas seguras para `ADMIN_PWD` y `DB_PASSWORD`
- [ ] Configurar CORS con dominios específicos
- [ ] Habilitar SSL/TLS en todas las conexiones
- [ ] Configurar rate limiting apropiado
- [ ] Implementar monitoreo y alertas

### Mantenimiento

```bash
# Limpiar datos expirados (ejecutar periódicamente)
make db-cleanup --force

# Rotar claves JWK (ocasionalmente)
make gen-jwk

# Backup regular
make backup-db
```

## 📚 Documentación Adicional

- [API Documentation](/_docs/api.md) (TODO)
- [OIDC Flow Examples](/_docs/oidc-examples.md) (TODO)
- [Admin Panel Guide](/_docs/admin-guide.md) (TODO)

## 🤝 Contribuir

1. Fork el repositorio
2. Crear rama feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -m 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

## 📄 Licencia

MIT License - ver [LICENSE](LICENSE) para detalles.

---

**¿Necesitas ayuda?**
- 🐛 Reportar bugs en [Issues](../../issues)
- 💬 Preguntas en [Discussions](../../discussions)
- 📧 Email: [<EMAIL>]
