# Makefile para Cerebro IDP
.PHONY: help install dev build start test clean docker-up docker-down db-setup db-reset db-cleanup

COMPOSE := $(shell command -v docker-compose >/dev/null 2>&1 && echo docker-compose || echo docker compose)

# Variables
DOCKER_COMPOSE_FILE = docker-compose.dev.yml
DB_CONTAINER_NAME = cerebro-db
REDIS_CONTAINER_NAME = cerebro-redis

# Colores para output
GREEN = \033[0;32m
YELLOW = \033[1;33m
RED = \033[0;31m
BLUE = \033[0;34m
NC = \033[0m # No Color

# Default target
help: ## 📚 Mostrar esta ayuda
	@echo "$(GREEN)🧠 Cerebro Identity Provider - Comandos Disponibles$(NC)"
	@echo ""
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "$(YELLOW)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(GREEN)Ejemplos de uso:$(NC)"
	@echo "  make dev-full    # Setup completo + desarrollo"
	@echo "  make db-reset    # Resetear base de datos"
	@echo "  make health      # Health check completo"
	@echo "  make clean       # Limpiar todo"

##@ 🚀 Desarrollo
install: ## 📦 Instalar dependencias
	@echo "$(GREEN)📦 Instalando dependencias...$(NC)"
	npm install

dev: ## 🔄 Ejecutar en modo desarrollo
	@echo "$(GREEN)🔄 Iniciando desarrollo...$(NC)"
	npm run dev

dev-full: docker-up db-setup dev ## 🎯 Setup completo + desarrollo
	@echo "$(GREEN)🎯 Desarrollo completo iniciado!$(NC)"

build: ## 🏗️ Compilar proyecto
	@echo "$(GREEN)🏗️ Compilando proyecto...$(NC)"
	npm run build

start: ## ▶️ Ejecutar en producción
	@echo "$(GREEN)▶️ Iniciando servidor...$(NC)"
	npm run start

##@ 🐳 Docker
docker-up: ## 🐳 Levantar servicios Docker
	@echo "$(GREEN)🐳 Levantando servicios Docker...$(NC)"
	$(COMPOSE) -f $(DOCKER_COMPOSE_FILE) up -d
	@echo "$(GREEN)⏳ Esperando que los servicios estén listos...$(NC)"
	sleep 10

docker-down: ## 🛑 Parar servicios Docker
	@echo "$(RED)🛑 Parando servicios Docker...$(NC)"
	$(COMPOSE) -f $(DOCKER_COMPOSE_FILE) down

docker-logs: ## 📋 Ver logs de Docker
	@echo "$(YELLOW)📋 Logs de servicios Docker:$(NC)"
	$(COMPOSE) -f $(DOCKER_COMPOSE_FILE) logs -f

docker-restart: docker-down docker-up ## 🔄 Reiniciar servicios Docker

docker-clean: ## 🧹 Limpiar Docker (volumes, images, etc)
	@echo "$(RED)🧹 Limpiando Docker...$(NC)"
	$(COMPOSE) -f $(DOCKER_COMPOSE_FILE) down -v --remove-orphans
	docker system prune -f

##@ 🗄️ Base de Datos
db-setup: ## 📊 Setup inicial de base de datos
	@echo "$(GREEN)📊 Configurando base de datos...$(NC)"
	npm run migrate
	npm run seed
	npm run gen:jwk
	@echo "$(GREEN)✅ Base de datos configurada!$(NC)"

# db-reset: ## 🔄 Resetear base de datos completamente
# 	@echo "$(YELLOW)🔄 Reseteando base de datos...$(NC)"
# 	npm run db:reset
# 	@echo "$(GREEN)✅ Base de datos reseteada!$(NC)"

db-migrate: ## 🔄 Ejecutar migraciones
	@echo "$(GREEN)🔄 Ejecutando migraciones...$(NC)"
	npm run migrate

db-migrate-status: ## 📊 Estado de migraciones
	@echo "$(GREEN)📊 Estado de migraciones:$(NC)"
	npm run migrate:status

db-seed: ## 🌱 Ejecutar seeds
	@echo "$(GREEN)🌱 Ejecutando seeds...$(NC)"
	npm run seed

db-seed-clean: ## 🧹 Limpiar datos de desarrollo
	@echo "$(YELLOW)🧹 Limpiando datos de desarrollo...$(NC)"
	npm run seed:clean

# db-cleanup: ## 🧹 Limpiar datos expirados (dry-run)
# 	@echo "$(YELLOW)🧹 Analizando datos a limpiar...$(NC)"
# 	npm run db:cleanup:stats
# 	@echo ""
# 	@echo "$(YELLOW)Para ejecutar la limpieza real, usa: make db-cleanup-force$(NC)"

# db-cleanup-force: ## 🧹 Limpiar datos expirados (ejecutar)
# 	@echo "$(RED)🧹 Limpiando base de datos (ejecutando)...$(NC)"
# 	npm run db:cleanup:force

# db-cleanup-full: ## 🧹 Limpieza completa + optimización
# 	@echo "$(BLUE)🧹 Limpieza completa con optimización...$(NC)"
# 	npm run db:cleanup:full

##@ 🔐 Seguridad & Claves
gen-jwk: ## 🔑 Generar nueva JWK
	@echo "$(GREEN)🔑 Generando nueva JWK...$(NC)"
	npm run gen:jwk

##@ 🧪 Testing & Calidad
# test: ## 🧪 Ejecutar tests
# 	@echo "$(GREEN)🧪 Ejecutando tests...$(NC)"
# 	npm run test

# test-unit: ## 🔬 Tests unitarios
# 	@echo "$(GREEN)🔬 Ejecutando tests unitarios...$(NC)"
# 	npm run test:unit

# test-integration: ## 🔗 Tests de integración
# 	@echo "$(GREEN)🔗 Ejecutando tests de integración...$(NC)"
# 	npm run test:integration

# lint: ## 🔍 Linter
# 	@echo "$(GREEN)🔍 Ejecutando linter...$(NC)"
# 	npm run lint

# lint-fix: ## 🔧 Corregir errores de linting
# 	@echo "$(GREEN)🔧 Corrigiendo errores de linting...$(NC)"
# 	npm run lint:fix

type-check: ## 📝 Verificar tipos TypeScript
	@echo "$(GREEN)📝 Verificando tipos...$(NC)"
	npm run type-check

##@ 📊 Monitoreo & Debug
health: ## 🏥 Health check completo
	@echo "$(GREEN)🏥 Ejecutando health check...$(NC)"
	npm run health

health-json: ## 🏥 Health check (formato JSON)
	@echo "$(GREEN)🏥 Health check (JSON):$(NC)"
	npm run health:json

health-continuous: ## 🔄 Monitoreo continuo
	@echo "$(BLUE)🔄 Iniciando monitoreo continuo...$(NC)"
	npm run health:continuous

health-db: ## 🗄️ Check específico de base de datos
	@npm run health:database

health-redis: ## 🔴 Check específico de Redis
	@npm run health:redis

health-oidc: ## 🌐 Check específico de OIDC
	@npm run health:oidc

health-jwk: ## 🔑 Check específico de JWK
	@npm run health:jwk

logs: ## 📋 Ver logs de la aplicación
	@echo "$(YELLOW)📋 Logs de la aplicación:$(NC)"
	tail -f logs/app.log 2>/dev/null || echo "$(YELLOW)No hay archivos de log aún$(NC)"

db-status: ## 📊 Estado de base de datos
	@echo "$(GREEN)📊 Estado de la base de datos:$(NC)"
	@$(COMPOSE) -f $(DOCKER_COMPOSE_FILE) exec -T db mariadb -u cerebro -pcerebro cerebro -e "SHOW TABLES;" 2>/dev/null || echo "$(RED)Base de datos no disponible$(NC)"

##@ 📄 Documentación
docs: ## 📚 Generar documentación
	@echo "$(GREEN)📚 Generando documentación...$(NC)"
	npm run util:tree

##@ 🧹 Limpieza
clean: ## 🧹 Limpiar archivos generados
	@echo "$(RED)🧹 Limpiando archivos generados...$(NC)"
	rm -rf dist/
	rm -rf node_modules/.cache/
	rm -rf logs/*.log 2>/dev/null || true
	@echo "$(GREEN)✅ Limpieza completada!$(NC)"

clean-all: clean docker-clean ## 🧹 Limpieza completa (incluye Docker)
	@echo "$(RED)🧹 Limpieza completa...$(NC)"
	@echo "$(GREEN)✅ Limpieza completa terminada!$(NC)"



##@ 🛠️ Utilidades
shell-db: ## 💻 Shell MySQL/MariaDB
	@echo "$(GREEN)💻 Conectando a MySQL...$(NC)"
	$(COMPOSE) -f $(DOCKER_COMPOSE_FILE) exec db mariadb -u cerebro -pcerebro cerebro

shell-redis: ## 💻 Shell Redis
	@echo "$(GREEN)💻 Conectando a Redis...$(NC)"
	$(COMPOSE) -f $(DOCKER_COMPOSE_FILE) exec redis redis-cli

backup-db: ## 💾 Backup de base de datos
	@echo "$(GREEN)💾 Creando backup...$(NC)"
	mkdir -p backups
	$(COMPOSE) -f $(DOCKER_COMPOSE_FILE) exec -T db mysqldump -u cerebro -pcerebro cerebro > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "$(GREEN)✅ Backup creado en backups/$(NC)"

restore-db: ## 🔄 Restaurar base de datos
	@echo "$(YELLOW)🔄 Restaurar base de datos:$(NC)"
	@echo "Uso: make restore-db BACKUP_FILE=backups/backup_YYYYMMDD_HHMMSS.sql"
ifdef BACKUP_FILE
	@if [ -f "$(BACKUP_FILE)" ]; then \
		echo "$(GREEN)Restaurando desde $(BACKUP_FILE)...$(NC)"; \
		$(COMPOSE) -f $(DOCKER_COMPOSE_FILE) exec -T db mysql -u cerebro -pcerebro cerebro < $(BACKUP_FILE); \
		echo "$(GREEN)✅ Base de datos restaurada!$(NC)"; \
	else \
		echo "$(RED)❌ Archivo $(BACKUP_FILE) no encontrado$(NC)"; \
		exit 1; \
	fi
else
	@echo "$(RED)❌ Especifica BACKUP_FILE$(NC)"
	@echo "Ejemplo: make restore-db BACKUP_FILE=backups/backup_20241201_120000.sql"
endif

##@ ⚙️ Configuración
setup-env: ## ⚙️ Configurar archivo .env
	@echo "$(GREEN)⚙️ Configurando archivo .env...$(NC)"
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "$(YELLOW)📝 Archivo .env creado desde .env.example$(NC)"; \
		echo "$(YELLOW)⚠️  Recuerda configurar las variables según tu entorno$(NC)"; \
		echo "$(BLUE)Variables importantes a cambiar:$(NC)"; \
		echo "  - ADMIN_PWD"; \
		echo "  - JWT_SECRET"; \
		echo "  - DB_PASSWORD"; \
	else \
		echo "$(YELLOW)⚠️  El archivo .env ya existe$(NC)"; \
	fi

check-env: ## ✅ Verificar configuración de entorno
	@echo "$(GREEN)✅ Verificando configuración...$(NC)"
	@if [ -f .env ]; then \
		echo "$(GREEN)✓ Archivo .env encontrado$(NC)"; \
	else \
		echo "$(RED)❌ Archivo .env no encontrado - ejecuta: make setup-env$(NC)"; \
		exit 1; \
	fi
	@npm run util:check-env

check-deps: ## ✅ Verificar dependencias del sistema
	@echo "$(GREEN)✅ Verificando dependencias...$(NC)"
	@echo "Verificando Node.js..."
	@node --version || (echo "$(RED)❌ Node.js no encontrado$(NC)" && exit 1)
	@echo "Verificando npm..."
	@npm --version || (echo "$(RED)❌ npm no encontrado$(NC)" && exit 1)
	@echo "Verificando Docker..."
	@docker --version || (echo "$(RED)❌ Docker no encontrado$(NC)" && exit 1)
	@echo "Verificando Docker Compose..."
	@$(COMPOSE) version || (echo "$(RED)❌ Docker Compose no encontrado$(NC)" && exit 1)
	@echo "$(GREEN)✅ Todas las dependencias están disponibles$(NC)"

##@ 🔧 Troubleshooting
fix-permissions: ## 🔧 Arreglar permisos de Docker
	@echo "$(YELLOW)🔧 Arreglando permisos...$(NC)"
	sudo chown -R $(shell id -u):$(shell id -g) .
	@echo "$(GREEN)✅ Permisos arreglados$(NC)"

reset-docker: ## 🔄 Reset completo de Docker
	@echo "$(RED)🔄 Reset completo de Docker...$(NC)"
	$(COMPOSE) -f $(DOCKER_COMPOSE_FILE) down -v --remove-orphans
	docker system prune -f --volumes
	docker network prune -f
	@echo "$(GREEN)✅ Docker reseteado$(NC)"

troubleshoot: ## 🔍 Diagnóstico de problemas
	@echo "$(BLUE)🔍 Ejecutando diagnóstico...$(NC)"
	@echo ""
	@echo "$(YELLOW)=== Verificando dependencias ===$(NC)"
	@make check-deps
	@echo ""
	@echo "$(YELLOW)=== Verificando configuración ===$(NC)"
	@make check-env
	@echo ""
	@echo "$(YELLOW)=== Estado de servicios Docker ===$(NC)"
	@$(COMPOSE) -f $(DOCKER_COMPOSE_FILE) ps
	@echo ""
	@echo "$(YELLOW)=== Health check ===$(NC)"
	@make health || echo "$(RED)❌ Health check falló$(NC)"
	@echo ""
	@echo "$(GREEN)🎉 Diagnóstico completado$(NC)"

##@ 📈 Desarrollo avanzado
dev-reset: ## 🔄 Reset completo de desarrollo
	@echo "$(YELLOW)🔄 Reset completo de desarrollo...$(NC)"
	make docker-down
	make clean
	make docker-up
	make db-setup
	@echo "$(GREEN)✅ Entorno de desarrollo reseteado$(NC)"

dev-logs: ## 📋 Logs de desarrollo (app + docker)
	@echo "$(BLUE)📋 Logs de desarrollo:$(NC)"
	@echo "$(YELLOW)--- Logs de Docker ---$(NC)"
	@make docker-logs &
	@echo "$(YELLOW)--- Logs de aplicación ---$(NC)"
	@make logs

dev-monitor: ## 📊 Monitoreo de desarrollo
	@echo "$(BLUE)📊 Iniciando monitoreo de desarrollo...$(NC)"
	@echo "Presiona Ctrl+C para parar"
	@make health-continuous

##@ 📋 Información
info: ## ℹ️ Información del proyecto
	@echo "$(BLUE)🧠 Cerebro Identity Provider$(NC)"
	@echo ""
	@echo "$(YELLOW)Versión:$(NC) $(shell node -p "require('./package.json').version")"
	@echo "$(YELLOW)Node.js:$(NC) $(shell node --version)"
	@echo "$(YELLOW)npm:$(NC) $(shell npm --version)"
	@echo "$(YELLOW)Entorno:$(NC) $(shell echo $NODE_ENV || echo 'development')"
	@echo ""
	@echo "$(YELLOW)Puertos por defecto:$(NC)"
	@echo "  - API: http://localhost:4000"
	@echo "  - MySQL: localhost:3306"
	@echo "  - Redis: localhost:6379"
	@echo "  - Adminer: http://localhost:8080"
	@echo ""
	@echo "$(YELLOW)URLs importantes:$(NC)"
	@echo "  - Health: http://localhost:4000/health"
	@echo "  - OIDC Config: http://localhost:4000/.well-known/openid-configuration"
	@echo "  - JWK: http://localhost:4000/jwks.json"

version: ## 📦 Mostrar versión
	@echo "$(GREEN)📦 Versión:$(NC) $(shell node -p "require('./package.json').version")"

# Targets especiales
.DEFAULT_GOAL := help

# Evitar errores con archivos que tienen nombres de targets
.PHONY: clean logs docs info version
