// scripts/seed.ts
import argon2 from 'argon2';
import { pool } from '../src/db.js';
import { logger } from '../src/logger.js';

async function runSeeds() {
  logger.info('🌱 Starting seed process...');

  try {
    // 1. Actualizar password del admin
    const adminPassword = process.env.ADMIN_PWD || 'SuperSegura123!';
    const adminHash = await argon2.hash(adminPassword, {
      type: argon2.argon2id,
      memoryCost: 2 ** 16, // 64 MB
      timeCost: 3,
      parallelism: 1,
    });

    const [updateResult]: any = await pool.query(
      'UPDATE users SET password_hash = ?, updated_at = NOW() WHERE email = ?',
      [adminHash, '<EMAIL>']
    );

    if (updateResult.affectedRows > 0) {
      logger.info('✅ Admin password updated successfully');
    } else {
      logger.warn('⚠️  Admin user not found, creating...');

      // Crear admin si no existe
      await pool.query(
        'INSERT INTO users (email, password_hash, display_name) VALUES (?, ?, ?)',
        ['<EMAIL>', adminHash, 'System Administrator']
      );

      // Asignar rol ADMIN
      const [[adminUser]]: any[] = await pool.query(
        'SELECT id FROM users WHERE email = ?',
        ['<EMAIL>']
      );

      await pool.query(
        'INSERT IGNORE INTO user_roles (user_id, role_id) VALUES (?, 1)',
        [adminUser.id]
      );

      logger.info('✅ Admin user created successfully');
    }

    // 2. Verificar que todos los roles básicos existen
    const requiredRoles = [
      { id: 1, name: 'ADMIN', description: 'Administrador del sistema' },
      { id: 2, name: 'CLIENT', description: 'Cliente estándar' },
      { id: 3, name: 'PRO', description: 'Cliente con funcionalidades premium' }
    ];

    for (const role of requiredRoles) {
      await pool.query(
        'INSERT IGNORE INTO roles (id, name, description) VALUES (?, ?, ?)',
        [role.id, role.name, role.description]
      );
    }
    logger.info('✅ Basic roles verified');

    // 3. Verificar features básicas
    const requiredFeatures = [
      { id: 1, code: 'DASHBOARD', description: 'Acceso al dashboard principal', is_active: true },
      { id: 2, code: 'PREMIUM', description: 'Funciones premium y avanzadas', is_active: true },
      { id: 3, code: 'ADMIN_PANEL', description: 'Acceso al panel de administración', is_active: true },
      { id: 4, code: 'API_ACCESS', description: 'Acceso a la API', is_active: true },
      { id: 5, code: '2FA', description: 'Autenticación de dos factores', is_active: false }
    ];

    for (const feature of requiredFeatures) {
      await pool.query(
        'INSERT IGNORE INTO features (id, code, description, is_active) VALUES (?, ?, ?, ?)',
        [feature.id, feature.code, feature.description, feature.is_active]
      );
    }
    logger.info('✅ Basic features verified');

    // 4. Crear clientes OIDC adicionales para desarrollo
    const devClients = [
      {
        client_id: 'app-web-local',
        name: 'Web App Local',
        redirect_uris: ['http://localhost:3000/auth/callback', 'http://localhost:5173/auth/callback'],
        allowed_scopes: ['openid', 'profile', 'email', 'offline_access'],
        require_pkce: true
      },
      {
        client_id: 'app-mobile-local',
        name: 'Mobile App Local',
        redirect_uris: ['com.cerebro.app://auth/callback', 'http://localhost:8080/auth/callback'],
        allowed_scopes: ['openid', 'profile', 'email'],
        require_pkce: true
      }
    ];

    for (const client of devClients) {
      await pool.query(
        'INSERT IGNORE INTO oidc_clients (client_id, name, redirect_uris, allowed_scopes, require_pkce, is_active) VALUES (?, ?, ?, ?, ?, ?)',
        [
          client.client_id,
          client.name,
          JSON.stringify(client.redirect_uris),
          JSON.stringify(client.allowed_scopes),
          client.require_pkce,
          true
        ]
      );
    }
    logger.info('✅ Development OIDC clients verified');

    // 5. Configuraciones del sistema por defecto
    const systemConfigs = [
      { key: 'maintenance_mode', value: 'false', type: 'boolean', description: 'Modo de mantenimiento del sistema' },
      { key: 'max_login_attempts', value: '5', type: 'integer', description: 'Máximo número de intentos de login' },
      { key: 'session_timeout', value: '3600', type: 'integer', description: 'Timeout de sesión en segundos' },
      { key: 'jwk_rotation_days', value: '90', type: 'integer', description: 'Días para rotación automática de JWK' },
      { key: 'password_min_length', value: '8', type: 'integer', description: 'Longitud mínima de contraseña' },
      { key: 'enable_registration', value: 'false', type: 'boolean', description: 'Permitir auto-registro de usuarios' },
      { key: 'enable_2fa', value: 'false', type: 'boolean', description: 'Habilitar autenticación de dos factores' }
    ];

    for (const config of systemConfigs) {
      await pool.query(
        'INSERT IGNORE INTO system_config (config_key, config_value, config_type, description) VALUES (?, ?, ?, ?)',
        [config.key, config.value, config.type, config.description]
      );
    }
    logger.info('✅ System configuration verified');

    // 6. Asegurar que todos los usuarios tengan al menos rol CLIENT
    const [userRoleResult]: any = await pool.query(`
      INSERT IGNORE INTO user_roles (user_id, role_id)
      SELECT u.id, 2
      FROM users u
      LEFT JOIN user_roles ur ON ur.user_id = u.id AND ur.role_id = 2
      WHERE ur.user_id IS NULL
    `);

    if (userRoleResult.affectedRows > 0) {
      logger.info(`✅ Assigned CLIENT role to ${userRoleResult.affectedRows} users`);
    }

    // 7. Crear usuario de prueba en desarrollo
    if (process.env.NODE_ENV === 'development') {
      const testUserEmail = '<EMAIL>';
      const testPassword = 'test123456';
      const testHash = await argon2.hash(testPassword, { type: argon2.argon2id });

      try {
        await pool.query(
          'INSERT IGNORE INTO users (email, password_hash, display_name) VALUES (?, ?, ?)',
          [testUserEmail, testHash, 'Test User']
        );

        // Asignar rol CLIENT al usuario de prueba
        const [[testUser]]: any[] = await pool.query(
          'SELECT id FROM users WHERE email = ?',
          [testUserEmail]
        );

        if (testUser) {
          await pool.query(
            'INSERT IGNORE INTO user_roles (user_id, role_id) VALUES (?, 2)',
            [testUser.id]
          );
          logger.info('✅ Test user created for development');
        }
      } catch (error) {
        // Usuario ya existe, está bien
        logger.debug('Test user already exists');
      }
    }

    // 8. Estadísticas finales
    const stats = await getSeededStats();
    logger.info({
      stats,
      environment: process.env.NODE_ENV || 'development'
    }, '🎉 Seed process completed successfully');

  } catch (error: any) {
    logger.error({ error }, '❌ Seed process failed');
    throw error;
  } finally {
    await pool.end();
  }
}

async function getSeededStats() {
  const queries = [
    { name: 'users', query: 'SELECT COUNT(*) as count FROM users' },
    { name: 'roles', query: 'SELECT COUNT(*) as count FROM roles' },
    { name: 'features', query: 'SELECT COUNT(*) as count FROM features' },
    { name: 'oidc_clients', query: 'SELECT COUNT(*) as count FROM oidc_clients' },
    { name: 'system_configs', query: 'SELECT COUNT(*) as count FROM system_config' }
  ];

  const stats: Record<string, number> = {};

  for (const query of queries) {
    try {
      const [[result]]: any[] = await pool.query(query.query);
      stats[query.name] = result.count;
    } catch {
      stats[query.name] = 0;
    }
  }

  return stats;
}

// Función para limpiar datos de desarrollo
async function cleanDevelopmentData() {
  if (process.env.NODE_ENV === 'production') {
    logger.error('❌ Cannot clean data in production environment');
    return;
  }

  logger.info('🧹 Cleaning development data...');

  try {
    // Limpiar datos excepto admin y configuraciones básicas
    await pool.query('DELETE FROM user_roles WHERE user_id > 1');
    await pool.query('DELETE FROM users WHERE id > 1');
    await pool.query('DELETE FROM auth_codes');
    await pool.query('DELETE FROM refresh_tokens');
    await pool.query('DELETE FROM oauth_tokens');
    await pool.query('DELETE FROM admin_sessions');
    await pool.query('DELETE FROM audit_logs');

    logger.info('✅ Development data cleaned');
  } catch (error) {
    logger.error({ error }, '❌ Failed to clean development data');
  } finally {
    await pool.end();
  }
}

// CLI handling
const command = process.argv[2];

switch (command) {
  case 'clean':
    cleanDevelopmentData();
    break;
  case 'stats':
    getSeededStats().then(stats => {
      console.log('📊 Current database stats:', stats);
      pool.end();
    });
    break;
  default:
    runSeeds();
    break;
}

// Help
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
🌱 Cerebro IDP Seed Tool

Usage:
  npm run seed [command]

Commands:
  (default)     Run all seeds
  clean         Clean development data (dev only)
  stats         Show database statistics

Examples:
  npm run seed              # Run all seeds
  npm run seed clean        # Clean dev data
  npm run seed stats        # Show stats

Environment Variables:
  ADMIN_PWD                 # Admin password (default: SuperSegura123!)
  NODE_ENV                  # Environment (affects test user creation)
  `);
  process.exit(0);
}
