// scripts/gen-jwk.ts
import { ensureActiveJwk } from '../src/crypto/jwk.js';
import { pool } from '../src/db.js';
import { logger } from '../src/logger.js';
import { env } from '../src/env.js';

async function generateJwk() {
  logger.info('🔑 Generating new JWK...');

  try {
    // Verificar si ya existe una JWK activa
    const [existingKeys]: any[] = await pool.query(
      'SELECT kid, created_at FROM jwk_keys WHERE is_active = 1 LIMIT 1'
    );

    if (existingKeys.length > 0) {
      const existing = existingKeys[0];
      const createdDate = new Date(existing.created_at);
      const daysSinceCreation = Math.floor((Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24));

      logger.info({
        existingKid: existing.kid,
        createdDate: createdDate.toISOString(),
        daysSinceCreation
      }, 'Found existing active JWK');

      // Preguntar si queremos rotar
      const shouldRotate = process.argv.includes('--force') || process.argv.includes('--rotate');

      if (!shouldRotate && daysSinceCreation < 7) {
        logger.info('✅ Recent JWK exists and --force not specified, skipping generation');
        logger.info('💡 Use --force to generate anyway or --rotate to rotate keys');
        return;
      }

      if (shouldRotate) {
        logger.info('🔄 Rotating JWK keys...');
        await rotateJwk();
        return;
      }
    }

    // Generar nueva JWK
    const newKey = await ensureActiveJwk();

    logger.info({
      kid: newKey.kid,
      algorithm: newKey.alg,
      keyUse: newKey.key_use,
      keyType: newKey.kty || 'RSA'
    }, '✅ New JWK generated successfully');

    // Actualizar variable de entorno si es necesario
    if (env.JWK_ACTIVE_KID !== newKey.kid) {
      logger.info(`💡 Consider updating JWK_ACTIVE_KID in .env to: ${newKey.kid}`);
    }

    // Verificar que la JWK funciona
    await testJwkFunctionality(newKey.kid);

  } catch (error: any) {
    logger.error({ error }, '❌ Failed to generate JWK');
    process.exit(1);
  } finally {
    await pool.end();
  }
}

async function rotateJwk() {
  logger.info('🔄 Starting JWK rotation...');

  const connection = await pool.getConnection();
  try {
    await connection.beginTransaction();

    // 1. Desactivar JWK actual
    await connection.query('UPDATE jwk_keys SET is_active = 0 WHERE is_active = 1');
    logger.info('📴 Deactivated old JWK');

    // 2. Generar nueva JWK
    const newKey = await ensureActiveJwk();
    logger.info({ kid: newKey.kid }, '🔑 Generated new JWK');

    // 3. Marcar fecha de expiración para la clave anterior (opcional cleanup)
    const expirationDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 días
    await connection.query(
      'UPDATE jwk_keys SET expires_at = ? WHERE is_active = 0 AND expires_at IS NULL',
      [expirationDate]
    );

    await connection.commit();
    logger.info('✅ JWK rotation completed successfully');

    // Crear audit log de la rotación
    try {
      await connection.query(
        'INSERT INTO audit_logs (user_id, action, resource_type, resource_id, details) VALUES (?, ?, ?, ?, ?)',
        [
          null, // Sistema automático
          'jwk_rotated',
          'jwk_key',
          newKey.kid,
          JSON.stringify({
            reason: 'manual_rotation',
            timestamp: new Date().toISOString(),
            previous_keys_expired: expirationDate.toISOString()
          })
        ]
      );
    } catch (auditError) {
      logger.warn({ error: auditError }, 'Failed to create audit log for JWK rotation');
    }

  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
}

async function listJwks() {
  logger.info('📋 Listing all JWK keys...');

  try {
    const [keys]: any[] = await pool.query(`
      SELECT kid, alg, key_use, is_active, created_at, expires_at
      FROM jwk_keys
      ORDER BY created_at DESC
    `);

    if (keys.length === 0) {
      logger.info('No JWK keys found in database');
      return;
    }

    const activeKeys = keys.filter((k: any) => k.is_active);
    const inactiveKeys = keys.filter((k: any) => !k.is_active);

    logger.info({
      totalKeys: keys.length,
      activeKeys: activeKeys.length,
      inactiveKeys: inactiveKeys.length
    }, 'JWK Summary');

    // Mostrar claves activas
    if (activeKeys.length > 0) {
      logger.info('🟢 Active JWK Keys:');
      activeKeys.forEach((key: any) => {
        logger.info({
          kid: key.kid,
          algorithm: key.alg,
          keyUse: key.key_use,
          createdAt: new Date(key.created_at).toISOString()
        });
      });
    }

    // Mostrar claves inactivas
    if (inactiveKeys.length > 0) {
      logger.info('🔴 Inactive JWK Keys:');
      inactiveKeys.forEach((key: any) => {
        logger.info({
          kid: key.kid,
          algorithm: key.alg,
          keyUse: key.key_use,
          createdAt: new Date(key.created_at).toISOString(),
          expiresAt: key.expires_at ? new Date(key.expires_at).toISOString() : null
        });
      });
    }

  } catch (error) {
    logger.error({ error }, 'Failed to list JWK keys');
  } finally {
    await pool.end();
  }
}

async function cleanupExpiredJwks() {
  logger.info('🧹 Cleaning up expired JWK keys...');

  try {
    const [result]: any = await pool.query(
      'DELETE FROM jwk_keys WHERE is_active = 0 AND expires_at < NOW()'
    );

    const deletedCount = result.affectedRows || 0;

    if (deletedCount > 0) {
      logger.info(`✅ Cleaned up ${deletedCount} expired JWK keys`);
    } else {
      logger.info('✨ No expired JWK keys to clean up');
    }

  } catch (error) {
    logger.error({ error }, 'Failed to cleanup expired JWK keys');
  } finally {
    await pool.end();
  }
}

async function testJwkFunctionality(kid: string) {
  logger.info('🧪 Testing JWK functionality...');

  try {
    const { signIdToken } = await import('../src/crypto/jwk.js');

    // Test payload
    const testPayload = {
      iss: env.OIDC_ISSUER,
      aud: env.OIDC_AUDIENCE,
      sub: 'test-user-123',
      email: '<EMAIL>',
      exp: Math.floor(Date.now() / 1000) + 300 // 5 minutes
    };

    const token = await signIdToken(testPayload, kid);

    if (token && token.split('.').length === 3) {
      logger.info('✅ JWK functionality test passed');
    } else {
      throw new Error('Invalid JWT token generated');
    }

  } catch (error) {
    logger.error({ error }, '❌ JWK functionality test failed');
    throw error;
  }
}

// CLI handling
const command = process.argv[2];

async function main() {
  try {
    switch (command) {
      case 'list':
        await listJwks();
        break;

      case 'rotate':
        await rotateJwk();
        break;

      case 'cleanup':
        await cleanupExpiredJwks();
        break;

      case 'test':
        const testKid = process.argv[3];
        if (!testKid) {
          // Buscar JWK activa
          const [keys]: any[] = await pool.query(
            'SELECT kid FROM jwk_keys WHERE is_active = 1 LIMIT 1'
          );
          if (keys.length === 0) {
            throw new Error('No active JWK found for testing');
          }
          await testJwkFunctionality(keys[0].kid);
        } else {
          await testJwkFunctionality(testKid);
        }
        await pool.end();
        break;

      case 'generate':
      default:
        await generateJwk();
        break;
    }

  } catch (error) {
    logger.error({ error }, 'JWK operation failed');
    process.exit(1);
  }
}

// Help
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
🔑 Cerebro IDP JWK Management Tool

Usage:
  npm run gen:jwk [command] [options]

Commands:
  generate      Generate new JWK if none exists (default)
  rotate        Rotate active JWK (deactivate old, generate new)
  list          List all JWK keys
  cleanup       Remove expired JWK keys
  test [kid]    Test JWK functionality

Options:
  --force       Force generation even if recent JWK exists
  --rotate      Alias for 'rotate' command
  --help, -h    Show this help message

Examples:
  npm run gen:jwk                    # Generate JWK if needed
  npm run gen:jwk generate --force   # Force generate new JWK
  npm run gen:jwk rotate             # Rotate active JWK
  npm run gen:jwk list               # List all JWKs
  npm run gen:jwk cleanup            # Clean expired JWKs
  npm run gen:jwk test               # Test active JWK

Environment Variables:
  OIDC_ISSUER                       # JWT issuer
  OIDC_AUDIENCE                     # JWT audience
  JWK_ACTIVE_KID                    # Active key ID (optional)
  `);
  process.exit(0);
}

main();
