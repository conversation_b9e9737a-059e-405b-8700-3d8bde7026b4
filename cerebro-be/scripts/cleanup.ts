// scripts/cleanup.ts
import { pool } from '../src/db.js';
import { logger } from '../src/logger.js';

interface CleanupStats {
  refreshTokens: number;
  oauthTokens: number;
  adminSessions: number;
  authCodes: number;
  auditLogs: number;
  total: number;
}

async function runCleanup(): Promise<CleanupStats> {
  logger.info('🧹 Starting database cleanup...');

  const stats: CleanupStats = {
    refreshTokens: 0,
    oauthTokens: 0,
    adminSessions: 0,
    authCodes: 0,
    auditLogs: 0,
    total: 0
  };

  const connection = await pool.getConnection();

  try {
    await connection.beginTransaction();

    // 1. Limpiar refresh_tokens expirados
    const [refreshResult]: any = await connection.query(
      'DELETE FROM refresh_tokens WHERE expires_at < NOW() OR revoked = 1'
    );
    stats.refreshTokens = refreshResult.affectedRows || 0;
    logger.info(`🗑️  Cleaned ${stats.refreshTokens} expired/revoked refresh tokens`);

    // 2. Limpiar oauth_tokens expirados (si la tabla existe)
    try {
      const [oauthResult]: any = await connection.query(
        'DELETE FROM oauth_tokens WHERE expires_at < NOW() OR revoked = 1'
      );
      stats.oauthTokens = oauthResult.affectedRows || 0;
      logger.info(`🗑️  Cleaned ${stats.oauthTokens} expired/revoked oauth tokens`);
    } catch (error: any) {
      if (!error.message.includes("doesn't exist")) {
        throw error;
      }
      logger.debug('oauth_tokens table does not exist, skipping');
    }

    // 3. Limpiar admin_sessions expiradas
    try {
      const [sessionResult]: any = await connection.query(
        'DELETE FROM admin_sessions WHERE expires_at < NOW()'
      );
      stats.adminSessions = sessionResult.affectedRows || 0;
      logger.info(`🗑️  Cleaned ${stats.adminSessions} expired admin sessions`);
    } catch (error: any) {
      if (!error.message.includes("doesn't exist")) {
        throw error;
      }
      logger.debug('admin_sessions table does not exist, skipping');
    }

    // 4. Limpiar auth_codes expirados
    const [codesResult]: any = await connection.query(
      'DELETE FROM auth_codes WHERE expires_at < NOW()'
    );
    stats.authCodes = codesResult.affectedRows || 0;
    logger.info(`🗑️  Cleaned ${stats.authCodes} expired auth codes`);

    // 5. Limpiar audit_logs antiguos (más de 90 días por defecto)
    const retentionDays = Number(process.env.AUDIT_LOG_RETENTION_DAYS || 90);
    try {
      const [auditResult]: any = await connection.query(
        'DELETE FROM audit_logs WHERE timestamp < DATE_SUB(NOW(), INTERVAL ? DAY)',
        [retentionDays]
      );
      stats.auditLogs = auditResult.affectedRows || 0;
      logger.info(`🗑️  Cleaned ${stats.auditLogs} old audit logs (older than ${retentionDays} days)`);
    } catch (error: any) {
      if (!error.message.includes("doesn't exist")) {
        throw error;
      }
      logger.debug('audit_logs table does not exist, skipping');
    }

    // Calcular total
    stats.total = stats.refreshTokens + stats.oauthTokens + stats.adminSessions + stats.authCodes + stats.auditLogs;

    // Registrar la limpieza en audit_logs (si la tabla existe)
    try {
      await connection.query(
        `INSERT INTO audit_logs (user_id, action, resource_type, details)
         VALUES (NULL, 'system_cleanup', 'database', ?)`,
        [JSON.stringify({
          cleanup_stats: stats,
          retention_days: retentionDays,
          timestamp: new Date().toISOString()
        })]
      );
    } catch (error) {
      logger.debug('Could not log cleanup to audit_logs, table might not exist yet');
    }

    await connection.commit();

    logger.info({
      stats,
      retentionDays
    }, `✅ Database cleanup completed - ${stats.total} records removed`);

    return stats;

  } catch (error: any) {
    await connection.rollback();
    logger.error({ error }, '❌ Database cleanup failed');
    throw error;
  } finally {
    connection.release();
  }
}

async function optimizeTables(): Promise<void> {
  logger.info('⚡ Running table optimization...');

  const tables = [
    'users', 'roles', 'user_roles', 'features', 'role_features',
    'oidc_clients', 'auth_codes', 'refresh_tokens', 'jwk_keys',
    'audit_logs', 'oauth_tokens', 'admin_sessions', 'system_config'
  ];

  let optimized = 0;

  for (const table of tables) {
    try {
      await pool.query(`OPTIMIZE TABLE ${table}`);
      optimized++;
      logger.debug(`Optimized table: ${table}`);
    } catch (error: any) {
      if (error.message.includes("doesn't exist")) {
        logger.debug(`Table ${table} does not exist, skipping optimization`);
      } else {
        logger.warn({ error, table }, `Failed to optimize table ${table}`);
      }
    }
  }

  logger.info(`⚡ Table optimization completed - ${optimized}/${tables.length} tables optimized`);
}

async function showCleanupStats(): Promise<void> {
  logger.info('📊 Generating cleanup statistics...');

  try {
    const queries = [
      {
        name: 'Expired Refresh Tokens',
        query: 'SELECT COUNT(*) as count FROM refresh_tokens WHERE expires_at < NOW() OR revoked = 1'
      },
      {
        name: 'Expired Auth Codes',
        query: 'SELECT COUNT(*) as count FROM auth_codes WHERE expires_at < NOW()'
      },
      {
        name: 'Old Audit Logs (>90 days)',
        query: 'SELECT COUNT(*) as count FROM audit_logs WHERE timestamp < DATE_SUB(NOW(), INTERVAL 90 DAY)'
      },
      {
        name: 'Total Database Size',
        query: `SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables
                WHERE table_schema = DATABASE()`
      }
    ];

    const stats: Record<string, any> = {};

    for (const query of queries) {
      try {
        const [[result]]: any[] = await pool.query(query.query);
        stats[query.name] = query.name === 'Total Database Size' ?
          `${result.size_mb} MB` : result.count;
      } catch (error) {
        stats[query.name] = 'N/A';
      }
    }

    logger.info({ cleanupStats: stats }, '📊 Cleanup statistics');

  } catch (error) {
    logger.error({ error }, 'Failed to generate cleanup statistics');
  }
}

// CLI handling
const command = process.argv[2];
const force = process.argv.includes('--force');

async function main() {
  try {
    switch (command) {
      case 'stats':
        await showCleanupStats();
        break;

      case 'optimize':
        await optimizeTables();
        break;

      case 'full':
        await showCleanupStats();
        const stats = await runCleanup();
        await optimizeTables();

        if (stats.total > 0) {
          logger.info(`🎉 Full cleanup completed! Removed ${stats.total} records total`);
        } else {
          logger.info('✨ Database is already clean - no records to remove');
        }
        break;

      case 'run':
      default:
        if (!force) {
          logger.info('📋 Dry run - use --force to actually clean data');
          await showCleanupStats();
        } else {
          const stats = await runCleanup();

          if (stats.total > 0) {
            logger.info(`🎉 Cleanup completed! Removed ${stats.total} records`);
          } else {
            logger.info('✨ Database is already clean - no records to remove');
          }
        }
        break;
    }

  } catch (error) {
    logger.error({ error }, 'Cleanup process failed');
    process.exit(1);
  } finally {
    await pool.end();
    process.exit(0);
  }
}

// Ayuda
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
🧹 Database Cleanup Tool

Usage:
  npm run db:cleanup [command] [options]

Commands:
  run          Clean expired tokens and old data (default, dry-run)
  stats        Show what would be cleaned
  optimize     Optimize database tables
  full         Run cleanup + optimization

Options:
  --force      Actually perform cleanup (not just dry-run)
  --help, -h   Show this help message

Examples:
  npm run db:cleanup stats           # Show cleanup statistics
  npm run db:cleanup --force         # Actually clean the database
  npm run db:cleanup full --force    # Full cleanup with optimization
  npm run db:cleanup optimize        # Just optimize tables

Environment Variables:
  AUDIT_LOG_RETENTION_DAYS=90        # How many days to keep audit logs
  `);
  process.exit(0);
}

main();
