// scripts/migrate.ts
import { readFile } from "node:fs/promises";
import { join } from "node:path";
import { pool } from "../src/db.js";
import { logger } from "../src/logger.js";

// Lista de archivos de migración en orden
const migrationFiles = [
  "001_init.sql",
  "002_seed_minima.sql",
  "003_admin_improvements.sql",
];

interface MigrationResult {
  file: string;
  success: boolean;
  error?: string;
  executedStatements: number;
}

async function runMigrations() {
  logger.info("🚀 Starting database migrations...");

  const results: MigrationResult[] = [];
  let totalStatements = 0;

  try {
    // Crear tabla de migraciones si no existe
    await pool.query(`
      CREATE TABLE IF NOT EXISTS schema_migrations (
        version VARCHAR(255) PRIMARY KEY,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        execution_time_ms INT
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    for (const file of migrationFiles) {
      const filePath = join("migrations", file);
      const startTime = Date.now();

      logger.info(`📄 Applying migration: ${file}`);

      try {
        // Verificar si ya fue ejecutada
        const [[existing]]: any[] = await pool.query(
          'SELECT version FROM schema_migrations WHERE version = ?',
          [file]
        );

        if (existing) {
          logger.info(`⏭️  Migration ${file} already applied, skipping`);
          results.push({ file, success: true, executedStatements: 0 });
          continue;
        }

        const sql = await readFile(filePath, "utf8");

        // Split SQL statements más robusto
        const statements = sql
          .split(/;/) // Split por ;
          .map((stmt) => stmt.trim())
          .filter((stmt) => {
            if (stmt.length === 0 || stmt.match(/^\s*$/)) {
              return false; // Filtrar statements vacíos
            }

            // Filtrar statements que son solo comentarios
            const lines = stmt.split('\n').map(line => line.trim());
            const nonCommentLines = lines.filter(line =>
              line.length > 0 &&
              !line.startsWith('--') &&
              !line.match(/^\s*\/\*/)
            );

            // Si no hay líneas que no sean comentarios, filtrar
            if (nonCommentLines.length === 0) {
              return false;
            }

            // Filtrar DELIMITER statements
            if (stmt.toLowerCase().includes('delimiter')) {
              return false;
            }

            return true;
          });

        let executedCount = 0;

        // Ejecutar en transacción
        const connection = await pool.getConnection();
        try {
          await connection.beginTransaction();

          // Ejecutar cada statement
          for (const statement of statements) {
            if (statement.trim()) {
              try {
                await connection.query(statement);
                executedCount++;

                // Log cada 10 statements para migrations largas
                if (executedCount % 10 === 0) {
                  logger.debug(`Executed ${executedCount}/${statements.length} statements`);
                }
              } catch (statementError: any) {
                logger.error({
                  error: statementError,
                  statement: statement.substring(0, 500) + (statement.length > 500 ? "..." : ""),
                  file,
                  statementNumber: executedCount + 1
                }, "Failed to execute SQL statement");

                throw statementError;
              }
            }
          }

          // Registrar migración como completada
          const executionTime = Date.now() - startTime;
          await connection.query(
            'INSERT INTO schema_migrations (version, executed_at, execution_time_ms) VALUES (?, NOW(), ?)',
            [file, executionTime]
          );

          await connection.commit();
          totalStatements += executedCount;

          logger.info(`✅ Migration ${file} completed successfully (${executedCount} statements, ${executionTime}ms)`);
          results.push({ file, success: true, executedStatements: executedCount });

        } catch (error) {
          await connection.rollback();
          throw error;
        } finally {
          connection.release();
        }

      } catch (fileError: any) {
        logger.error({ error: fileError, file }, `❌ Migration ${file} failed`);
        results.push({
          file,
          success: false,
          error: fileError.message,
          executedStatements: 0
        });
        throw fileError;
      }
    }

    // Resumen final
    const successCount = results.filter(r => r.success).length;
    const failCount = results.filter(r => !r.success).length;

    logger.info({
      totalMigrations: migrationFiles.length,
      successful: successCount,
      failed: failCount,
      totalStatements,
      results
    }, "🎉 Migration process completed successfully");

    // Verificar estado de la base de datos
    await verifyDatabaseState();

  } catch (error: any) {
    logger.error({ error, results }, "💥 Migration process failed");
    process.exit(1);
  } finally {
    await pool.end();
  }
}

async function verifyDatabaseState() {
  try {
    logger.info("🔍 Verifying database state...");

    const checks = [
      { name: 'Users', query: 'SELECT COUNT(*) as count FROM users' },
      { name: 'Roles', query: 'SELECT COUNT(*) as count FROM roles' },
      { name: 'User Roles', query: 'SELECT COUNT(*) as count FROM user_roles' },
      { name: 'Features', query: 'SELECT COUNT(*) as count FROM features' },
      { name: 'OIDC Clients', query: 'SELECT COUNT(*) as count FROM oidc_clients' },
      { name: 'JWK Keys', query: 'SELECT COUNT(*) as count FROM jwk_keys' },
      { name: 'Auth Codes', query: 'SELECT COUNT(*) as count FROM auth_codes' },
      { name: 'Refresh Tokens', query: 'SELECT COUNT(*) as count FROM refresh_tokens' },
      { name: 'Audit Logs', query: 'SELECT COUNT(*) as count FROM audit_logs' },
      { name: 'OAuth Tokens', query: 'SELECT COUNT(*) as count FROM oauth_tokens' },
      { name: 'Admin Sessions', query: 'SELECT COUNT(*) as count FROM admin_sessions' },
      { name: 'System Config', query: 'SELECT COUNT(*) as count FROM system_config' },
    ];

    const stats: Record<string, number> = {};

    for (const check of checks) {
      try {
        const [[result]]: any[] = await pool.query(check.query);
        stats[check.name] = result.count;
      } catch (error) {
        stats[check.name] = -1; // Tabla no existe o error
      }
    }

    logger.info({ databaseStats: stats }, "📊 Database state verification completed");

    // Verificaciones críticas
    if (stats.Users === 0) {
      logger.warn("⚠️  No users found - remember to run seed script");
    }
    if (stats.Roles === 0) {
      logger.warn("⚠️  No roles found - remember to run seed script");
    }
    if (stats['JWK Keys'] === 0) {
      logger.warn("⚠️  No JWK keys found - remember to run gen:jwk script");
    }
    if (stats['System Config'] === 0) {
      logger.warn("⚠️  No system config found - might indicate migration issue");
    }

  } catch (error) {
    logger.error({ error }, "Failed to verify database state");
  }
}

// Función para rollback (útil para desarrollo)
async function rollbackMigration(migrationFile: string) {
  logger.info(`🔄 Rolling back migration: ${migrationFile}`);

  try {
    await pool.query('DELETE FROM schema_migrations WHERE version = ?', [migrationFile]);
    logger.info(`✅ Rollback completed for ${migrationFile}`);
    logger.warn("⚠️  Note: This only removes the migration record, not the actual changes");
  } catch (error) {
    logger.error({ error }, `❌ Rollback failed for ${migrationFile}`);
  } finally {
    await pool.end();
  }
}

// Función para mostrar estado de migraciones
async function showMigrationStatus() {
  logger.info("📋 Checking migration status...");

  try {
    const [rows]: any[] = await pool.query(`
      SELECT version, executed_at, execution_time_ms
      FROM schema_migrations
      ORDER BY executed_at
    `);

    if (rows.length === 0) {
      logger.info("No migrations have been executed yet");
    } else {
      logger.info({ migrations: rows }, "Migration status");
    }

    // Mostrar migraciones pendientes
    const executedMigrations = (rows as any[]).map(r => r.version);
    const pendingMigrations = migrationFiles.filter(f => !executedMigrations.includes(f));

    if (pendingMigrations.length > 0) {
      logger.info({ pendingMigrations }, "Pending migrations");
    } else {
      logger.info("✅ All migrations are up to date");
    }

  } catch (error) {
    logger.error({ error }, "Failed to check migration status");
  } finally {
    await pool.end();
  }
}

// CLI handling
const command = process.argv[2];

switch (command) {
  case 'rollback':
    const migrationToRollback = process.argv[3];
    if (!migrationToRollback) {
      console.error('Usage: npm run migrate rollback <migration_file>');
      process.exit(1);
    }
    rollbackMigration(migrationToRollback);
    break;

  case 'status':
    showMigrationStatus();
    break;

  case 'up':
  default:
    // Ejecutar migraciones
    runMigrations()
      .then(() => {
        logger.info("Migration process finished successfully");
        process.exit(0);
      })
      .catch((error) => {
        logger.error({ error }, "Migration process failed with unhandled error");
        process.exit(1);
      });
    break;
}
