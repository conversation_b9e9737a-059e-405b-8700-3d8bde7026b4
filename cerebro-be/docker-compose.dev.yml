# docker-compose.dev.yml
services:
  db:
    container_name: cerebro-db
    image: mariadb:11
    environment:
      MARIADB_ROOT_PASSWORD: root
      MARIADB_DATABASE: cerebro
      MARIADB_USER: cerebro
      MARIADB_PASSWORD: cerebro
    ports: ["3306:3306"]
    volumes: [dbdata:/var/lib/mysql]

  redis:
    container_name: cerebro-redis
    image: redis:7-alpine
    ports: ["6379:6379"]

  # --- <PERSON><PERSON><PERSON> de Adminer añadido ---
  adminer:
    container_name: cerebro-db-adminer
    image: adminer
    ports: ["8080:8080"] # Podrás acceder a Adminer en http://localhost:8080

volumes:
  dbdata: