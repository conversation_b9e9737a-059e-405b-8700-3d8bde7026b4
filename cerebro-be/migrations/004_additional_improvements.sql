-- migrations/004_additional_improvements.sql

-- 1. <PERSON>bla para rate limiting por usuario/IP
CREATE TABLE IF NOT EXISTS rate_limits (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  identifier VARCHAR(255) NOT NULL, -- IP, user_id, etc.
  identifier_type ENUM('ip', 'user', 'client') NOT NULL,
  endpoint VARCHAR(100) NOT NULL,
  attempts INT NOT NULL DEFAULT 1,
  window_start TIMESTAMP NOT NULL,
  expires_at TIMESTAMP NOT NULL,

  INDEX idx_identifier (identifier, identifier_type),
  INDEX idx_endpoint (endpoint),
  INDEX idx_expires (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2. Tabla para dispositivos confiables (para 2FA)
CREATE TABLE IF NOT EXISTS trusted_devices (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  user_id BIGINT UNSIGNED NOT NULL,
  device_fingerprint VARCHAR(255) NOT NULL,
  device_name VARCHAR(255) NULL,
  user_agent TEXT NULL,
  ip_address VARCHAR(45) NULL,
  trusted_until TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  UNIQUE KEY unique_user_device (user_id, device_fingerprint),
  CONSTRAINT fk_td_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_trusted (user_id, trusted_until),
  INDEX idx_fingerprint (device_fingerprint)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 3. Tabla para notificaciones del sistema
CREATE TABLE IF NOT EXISTS user_notifications (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  user_id BIGINT UNSIGNED NOT NULL,
  type VARCHAR(50) NOT NULL, -- 'security', 'login', 'admin', etc.
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  data JSON NULL, -- datos adicionales
  read_at TIMESTAMP NULL,
  expires_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  CONSTRAINT fk_un_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_unread (user_id, read_at),
  INDEX idx_type (type),
  INDEX idx_expires (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 4. Tabla para invitaciones de usuarios (si admins pueden invitar)
CREATE TABLE IF NOT EXISTS user_invitations (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  email VARCHAR(255) NOT NULL,
  invited_by BIGINT UNSIGNED NOT NULL,
  token VARCHAR(255) NOT NULL UNIQUE,
  roles JSON NOT NULL, -- roles a asignar
  expires_at TIMESTAMP NOT NULL,
  accepted_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  CONSTRAINT fk_ui_invited_by FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_email (email),
  INDEX idx_token (token),
  INDEX idx_expires (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 5. Mejorar tabla users con campos adicionales
ALTER TABLE users
ADD COLUMN password_expires_at TIMESTAMP NULL AFTER password_hash,
ADD COLUMN must_change_password BOOLEAN DEFAULT FALSE AFTER password_expires_at,
ADD COLUMN last_password_change TIMESTAMP NULL AFTER must_change_password;

-- 6. Índices adicionales para performance
CREATE INDEX idx_users_email_active ON users(email, is_active);
CREATE INDEX idx_refresh_tokens_user_active ON refresh_tokens(user_id, revoked, expires_at);
CREATE INDEX idx_auth_codes_expires ON auth_codes(expires_at);

-- 7. Campos adicionales en oidc_clients para mejor tracking
ALTER TABLE oidc_clients
ADD COLUMN usage_count BIGINT DEFAULT 0 AFTER last_used_at,
ADD COLUMN error_count INT DEFAULT 0 AFTER usage_count;
