-- migrations/001_init.sql

-- USERS
CREATE TABLE IF NOT EXISTS users (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,

  -- Autenticación
  email VARCHAR(255) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  display_name <PERSON><PERSON><PERSON><PERSON>(255) NULL,

  -- Información personal
  first_name VA<PERSON><PERSON>R(100) NULL,
  last_name <PERSON><PERSON><PERSON><PERSON>(100) NULL,
  avatar_url VARCHAR(500) NULL,
  phone VARCHAR(20) NULL,

  -- Estado y seguridad
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  is_verified BOOLEAN NOT NULL DEFAULT FALSE,
  email_verified_at TIMESTAMP NULL,
  last_login_at TIMESTAMP NULL,
  last_login_ip VARCHAR(45) NULL,
  failed_login_attempts INT DEFAULT 0,
  locked_until TIMESTAMP NULL,

  -- 2FA
  two_factor_secret VARCHAR(255) NULL,
  two_factor_enabled BOOLEAN DEFAULT FALSE,
  two_factor_recovery_codes JSON NULL,

  -- Preferencias
  locale VARCHAR(10) DEFAULT 'es-ES',
  timezone VARCHAR(50) DEFAULT 'Europe/Madrid',
  preferences JSON NULL,

  -- Metadatos
  deleted_at TIMESTAMP NULL,
  metadata JSON NULL,

  -- Tiempos
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_email (email),
  INDEX idx_created_at (created_at),
  INDEX idx_users_is_active (is_active),
  INDEX idx_users_last_login (last_login_at),
  INDEX idx_users_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ROLES
CREATE TABLE IF NOT EXISTS roles (
  id TINYINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(50) NOT NULL UNIQUE,
  description VARCHAR(255) NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- USER_ROLES (puente)
CREATE TABLE IF NOT EXISTS user_roles (
  user_id BIGINT UNSIGNED NOT NULL,
  role_id TINYINT UNSIGNED NOT NULL,
  assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (user_id, role_id),
  CONSTRAINT fk_ur_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  CONSTRAINT fk_ur_role FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
  INDEX idx_ur_user (user_id),
  INDEX idx_ur_role (role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- FEATURES
CREATE TABLE IF NOT EXISTS features (
  id SMALLINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  code VARCHAR(64) NOT NULL UNIQUE,
  description VARCHAR(255) NULL,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_code (code),
  INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- USER_FEATURES (opcional)
CREATE TABLE IF NOT EXISTS user_features (
  user_id BIGINT UNSIGNED NOT NULL,
  feature_id SMALLINT UNSIGNED NOT NULL,
  enabled BOOLEAN NOT NULL DEFAULT TRUE,
  granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (user_id, feature_id),
  CONSTRAINT fk_uf_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  CONSTRAINT fk_uf_feature FOREIGN KEY (feature_id) REFERENCES features(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS role_features (
  role_id TINYINT UNSIGNED NOT NULL,
  feature_id SMALLINT UNSIGNED NOT NULL,
  PRIMARY KEY (role_id, feature_id),
  CONSTRAINT fk_rf_role FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
  CONSTRAINT fk_rf_feature FOREIGN KEY (feature_id) REFERENCES features(id) ON DELETE CASCADE,
  INDEX idx_rf_role (role_id),
  INDEX idx_rf_feature (feature_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- OIDC CLIENTS
CREATE TABLE IF NOT EXISTS oidc_clients (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,

  -- OIDC
  client_id VARCHAR(100) NOT NULL UNIQUE,
  name VARCHAR(100) NOT NULL,
  client_secret VARCHAR(255) NULL,
  redirect_uris JSON NOT NULL,
  require_pkce BOOLEAN NOT NULL DEFAULT TRUE,
  allowed_scopes JSON NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,

  -- Configuración OAuth
  client_type ENUM('public', 'confidential') DEFAULT 'public',
  grant_types JSON DEFAULT '["authorization_code","refresh_token"]',
  response_types JSON DEFAULT '["code"]',

  -- URLs adicionales
  post_logout_redirect_uris JSON NULL,
  logo_uri VARCHAR(500) NULL,
  client_uri VARCHAR(500) NULL,
  policy_uri VARCHAR(500) NULL,
  tos_uri VARCHAR(500) NULL,

  -- Configuración de tokens
  access_token_ttl INT DEFAULT 3600,
  refresh_token_ttl INT DEFAULT 2592000,
  id_token_ttl INT DEFAULT 3600,

  -- Contacto y metadata
  contacts JSON NULL,
  owner_user_id BIGINT UNSIGNED NULL,

  -- Estado
  deleted_at TIMESTAMP NULL,
  last_used_at TIMESTAMP NULL,

  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_client_id (client_id),
  INDEX idx_active (is_active),

  CONSTRAINT fk_client_owner FOREIGN KEY (owner_user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- AUTH CODES
CREATE TABLE IF NOT EXISTS auth_codes (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  code VARCHAR(255) NOT NULL UNIQUE,
  client_id VARCHAR(100) NOT NULL,
  user_id BIGINT UNSIGNED NOT NULL,
  redirect_uri VARCHAR(500) NOT NULL,
  code_challenge VARCHAR(255) NOT NULL,
  code_challenge_method VARCHAR(10) NOT NULL DEFAULT 'S256',
  scope VARCHAR(500) NOT NULL DEFAULT 'openid',
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT fk_ac_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  CONSTRAINT fk_ac_client FOREIGN KEY (client_id) REFERENCES oidc_clients(client_id) ON DELETE CASCADE,
  INDEX idx_code (code),
  INDEX idx_expires (expires_at),
  INDEX idx_user (user_id),
  INDEX idx_client (client_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- REFRESH TOKENS
CREATE TABLE IF NOT EXISTS refresh_tokens (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  token VARCHAR(255) NOT NULL UNIQUE,
  user_id BIGINT UNSIGNED NOT NULL,
  client_id VARCHAR(100) NOT NULL,
  revoked BOOLEAN NOT NULL DEFAULT FALSE,
  expires_at TIMESTAMP NOT NULL,

  -- Tracking
  ip_address VARCHAR(45) NULL,
  user_agent TEXT NULL,
  device_id VARCHAR(100) NULL,
  device_name VARCHAR(255) NULL,

  -- Seguridad
  family_id VARCHAR(100) NULL, -- Para token rotation
  used_at TIMESTAMP NULL,
  revoked_by BIGINT UNSIGNED NULL,
  revoked_reason VARCHAR(255) NULL,

  -- Scopes
  scope VARCHAR(500) NULL,

  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  INDEX idx_token (token),
  INDEX idx_expires (expires_at),
  INDEX idx_user (user_id),
  INDEX idx_revoked (revoked),

  CONSTRAINT fk_rt_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  CONSTRAINT fk_rt_client FOREIGN KEY (client_id) REFERENCES oidc_clients(client_id) ON DELETE CASCADE,
  CONSTRAINT fk_revoked_by FOREIGN KEY (revoked_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- JWK KEYS
CREATE TABLE IF NOT EXISTS jwk_keys (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  kid VARCHAR(100) NOT NULL UNIQUE,
  kty VARCHAR(10) NOT NULL DEFAULT 'RSA',
  alg VARCHAR(20) NOT NULL DEFAULT 'RS256',
  key_use VARCHAR(10) NOT NULL DEFAULT 'sig' COMMENT 'sig|enc',
  public_jwk JSON NOT NULL,
  private_jwk JSON NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NULL,
  INDEX idx_kid (kid),
  INDEX idx_active (is_active),
  INDEX idx_alg_use (alg, key_use),
  INDEX idx_created_at (created_at),
  INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
