-- migrations/002_seed_minima.sql

-- ROLES
INSERT IGNORE INTO roles (id, name, description) VALUES
(1, 'ADMIN', 'Administrador del sistema'),
(2, 'CLIENT', 'Cliente estándar'),
(3, 'PRO', 'Cliente con funcionalidades premium');

-- FEATURES
INSERT IGNORE INTO features (id, code, description, is_active) VALUES
(1, 'DASHBOARD', 'Acceso al dashboard principal', TRUE),
(2, 'PREMIUM', 'Funciones premium y avanzadas', TRUE),
(3, 'ADMIN_PANEL', 'Acceso al panel de administración', TRUE);

-- ADMIN USER (temporal password hash - se actualizará en seed.ts)
INSERT IGNORE INTO users (id, email, password_hash, display_name) VALUES
(1, '<EMAIL>', '$2y$10$TEMP_HASH_WILL_BE_UPDATED', 'Admin');

-- Vincular ADMIN con rol ADMIN
INSERT IGNORE INTO user_roles (user_id, role_id) VALUES (1, 1);

-- Insertar relaciones rol-feature por defecto
INSERT IGNORE INTO features (id, code, description, is_active) VALUES
(1, 'DASHBOARD', 'Acceso al dashboard principal', TRUE),
(2, 'PREMIUM', 'Funciones premium y avanzadas', TRUE),
(3, 'ADMIN_PANEL', 'Acceso al panel de administración', TRUE),
(4, 'API_ACCESS', 'Acceso a la API', TRUE);

-- OIDC CLIENT por defecto
INSERT IGNORE INTO oidc_clients (client_id, name, client_secret, redirect_uris, require_pkce, allowed_scopes, is_active) VALUES
('app-a-local', 'App A Local', NULL, JSON_ARRAY('http://localhost:5173/auth/callback', 'http://localhost:3000/auth/callback'), TRUE, JSON_ARRAY('openid','profile','email'), TRUE);
