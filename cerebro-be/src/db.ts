// src/db.ts

import mysql, { PoolConnection, QueryError } from 'mysql2/promise';
// import { MysqlError } from 'mysql2'; // ✅ Import MysqlError from the base package
import { env } from './env.js';
import { logger } from './logger.js';

// Pool con configuración más robusta
export const pool = mysql.createPool({
  host: env.DB_HOST,
  port: env.DB_PORT,
  user: env.DB_USER,
  password: env.DB_PASSWORD,
  database: env.DB_NAME,

  // ✅ Configuración mejorada para evitar colgados
  connectionLimit: 10,
  queueLimit: 0,
  // ❌ 'timeout' no es una opción válida para el Pool. Se elimina.
  // Los timeouts de query se manejan en la función `queryWithTimeout`.

  // ✅ Timeouts específicos
  connectTimeout: 10000,  // 10 segundos para conectar

  // ✅ Character set
  charset: 'utf8mb4',
  timezone: '+00:00',

  // ✅ Otras opciones importantes
  multipleStatements: false,
  dateStrings: false,
  supportBigNumbers: true,
  bigNumberStrings: false,

  // 'reconnect' no es una opción de pool en mysql2. El pool maneja la reconexión.

  // ✅ SSL solo en producción
  ...(process.env.NODE_ENV === 'production' && {
    ssl: { rejectUnauthorized: false }
  }),
});

// ✅ Health check mejorado con timeout
export async function checkDatabaseHealth(): Promise<boolean> {
  let connection;
  try {
    // Obtener conexión con timeout
    connection = await pool.getConnection();

    // Query simple con timeout
    const [rows] = await connection.query('SELECT 1 as health');

    return Array.isArray(rows) && rows.length > 0;
  } catch (error: any) {
    logger.error({
      error: error.message,
      code: error.code,
      errno: error.errno
    }, 'Database health check failed');
    return false;
  } finally {
    // ✅ CRÍTICO: Siempre liberar la conexión
    if (connection) {
      connection.release();
    }
  }
}

// ✅ Graceful shutdown mejorado
export async function closeDatabasePool(): Promise<void> {
  try {
    await pool.end();
    logger.info('Database pool closed gracefully');
  } catch (error) {
    logger.error({ error }, 'Error closing database pool');
  }
}

// ✅ Event listeners mejorados
pool.on('connection', (connection: PoolConnection) => {
  logger.debug({ connectionId: connection.threadId }, 'New DB connection established');

  // ✅ El manejo de errores se hace en cada conexión, no en el pool
  connection.on('error', (error: QueryError) => {
    logger.error({
        error: { message: error.message, code: error.code }
    }, 'Database connection error');

    // Si es un error fatal, el pool lo manejará al intentar obtener una nueva conexión.
    if (error.code === 'PROTOCOL_CONNECTION_LOST') {
      logger.warn('Database connection lost. The pool will handle reconnecting.');
    }
  });
});


// ✅ Helper para queries con timeout automático
export async function queryWithTimeout<T = any>(
  sql: string,
  params?: any[],
  timeoutMs: number = 5000
): Promise<T> {
  return new Promise(async (resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error(`Query timeout after ${timeoutMs}ms: ${sql.substring(0, 50)}...`));
    }, timeoutMs);

    try {
      const [rows] = await pool.query(sql, params);
      clearTimeout(timeout);
      resolve(rows as T);
    } catch (error) {
      clearTimeout(timeout);
      reject(error);
    }
  });
}
