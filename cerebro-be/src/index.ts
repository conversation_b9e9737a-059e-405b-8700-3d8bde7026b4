// src/index.ts - Actualizado con nuevas rutas admin
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import pinoHttp from 'pino-http';
import rateLimit from 'express-rate-limit';
import { env } from './env.js';
import { logger } from './logger.js';
import { checkDatabaseHealth, closeDatabasePool } from './db.js';
import { errorHandler } from './middlewares/errorHandler.js';
import wellKnown from './routes/wellKnown.js';
import jwks from './routes/jwks.js';
import authorize from './routes/authorize.js';
import token from './routes/token.js';
import userinfo from './routes/userinfo.js';
import admin from './routes/admin.js';
import adminAuth from './routes/adminAuth.js';
import profile from './routes/profile.js';

const app = express();

// Middlewares básicos
app.use(helmet());
app.use(express.json());

// CORS - En desarrollo permitimos localhost, en producción será más restrictivo
const corsOptions = {
  origin: env.NODE_ENV === 'development'
    ? ['http://localhost:3000', 'http://localhost:*', 'http://localhost:8080']
    : false, // En producción, configurar dominios específicos
  credentials: true
};

app.use(cors(corsOptions));
app.use(pinoHttp({ logger }));

// Rate limiting más permisivo para admin en desarrollo
const rateLimitOptions = env.NODE_ENV === 'development'
  ? { windowMs: 60_000, max: 200 }  // Más requests en dev
  : { windowMs: 60_000, max: 120 }; // Límite normal en prod

app.use(rateLimit(rateLimitOptions));

// ============================================
// RUTAS OIDC ESTÁNDAR
// ============================================
app.use('/.well-known', wellKnown);
app.use('/jwks.json', jwks);
app.use('/authorize', authorize);
app.use('/token', token);
app.use('/userinfo', userinfo);
app.use('/user', profile);

// ============================================
// RUTAS ADMIN
// ============================================
app.use('/admin/auth', adminAuth); // Autenticación específica de admin
app.use('/admin', admin);          // CRUD de recursos (requiere auth)

// ============================================
// SIMPLE TEST ENDPOINT
// ============================================
app.get('/ping', (req, res) => {
  console.log('🏓 Ping requested');
  res.json({
    message: 'pong',
    timestamp: new Date().toISOString(),
    uptime: Math.floor(process.uptime())
  });
});

// ============================================
// HEALTH CHECK MEJORADO
// ============================================
app.get('/health', async (req, res) => {
  console.log('🏥 Health check requested at:', new Date().toISOString());

  // Timeout para toda la operación
  const healthTimeout = setTimeout(() => {
    console.log('❌ Health check timeout after 5 seconds');
    if (!res.headersSent) {
      res.status(503).json({
        status: 'timeout',
        error: 'Health check timed out',
        timestamp: new Date().toISOString()
      });
    }
  }, 5000);

  try {
    console.log('🔍 Starting database health check...');

    // Intentar health check con timeout propio
    const dbHealthPromise = checkDatabaseHealth();
    const dbHealth = await Promise.race([
      dbHealthPromise,
      new Promise<boolean>((_, reject) =>
        setTimeout(() => reject(new Error('DB health timeout')), 3000)
      )
    ]);

    console.log('📊 Database health result:', dbHealth);

    const health = {
      status: dbHealth ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: env.NODE_ENV,
      services: {
        database: dbHealth ? 'up' : 'down',
        redis: 'unknown', // Por ahora no verificamos Redis para evitar más colgados
      },
      uptime: Math.floor(process.uptime()),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
      }
    };

    clearTimeout(healthTimeout);

    const statusCode = dbHealth ? 200 : 503;
    console.log(`✅ Health check completed with status ${statusCode}`);

    if (!res.headersSent) {
      res.status(statusCode).json(health);
    }

  } catch (error: any) {
    console.error('💥 Health check error:', error.message);
    clearTimeout(healthTimeout);

    if (!res.headersSent) {
      res.status(500).json({
        status: 'error',
        error: 'Health check failed',
        message: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }
});

// ============================================
// ERROR HANDLING
// ============================================
app.use(errorHandler);

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'not_found',
    message: `Route ${req.method} ${req.originalUrl} not found`
  });
});

// ============================================
// SERVER STARTUP
// ============================================
console.log('Starting server setup...');
const server = app.listen(env.PORT, () => {
  console.log('Server callback executed');
  logger.info({
    port: env.PORT,
    environment: env.NODE_ENV,
    issuer: env.OIDC_ISSUER
  }, 'cerebro-idp server started');
  console.log('Server started successfully');
});

console.log('Server listen called, setting up error handlers...');

// Keep the process alive
setInterval(() => {
  console.log('Server is still running...');
}, 30000);

// ============================================
// GRACEFUL SHUTDOWN
// ============================================
const gracefulShutdown = async (signal: string) => {
  logger.info(`${signal} received, shutting down gracefully`);

  // Cerrar el servidor HTTP
  server.close(async () => {
    logger.info('HTTP server closed');

    try {
      // Cerrar conexiones de base de datos
      await closeDatabasePool();

      // Aquí podrías cerrar otras conexiones (Redis, etc.)

      logger.info('All connections closed, exiting process');
      process.exit(0);
    } catch (error) {
      logger.error({ error }, 'Error during graceful shutdown');
      process.exit(1);
    }
  });

  // Force exit after 10 seconds
  setTimeout(() => {
    logger.error('Forced exit after timeout');
    process.exit(1);
  }, 10000);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('UNCAUGHT EXCEPTION:', error);
  logger.fatal({ error }, 'Uncaught exception');
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('UNHANDLED REJECTION:', reason, promise);
  logger.fatal({ reason, promise }, 'Unhandled rejection');
  process.exit(1);
});
