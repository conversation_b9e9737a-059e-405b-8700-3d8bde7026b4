// src/middlewares/errorHandler.ts - versión corregida
import { Request, Response, NextFunction } from 'express';
import { logger } from '../logger.js';
import { ZodError } from 'zod';

export function errorHandler(error: Error, req: Request, res: Response, next: NextFunction) {
  const reqId = (req as any).id || 'unknown';

  // ✅ Zod validation errors corregido
  if (error instanceof ZodError) {
    logger.warn({ error: error.issues, reqId }, 'Validation error');
    return res.status(400).json({
      error: 'validation_error',
      details: error.issues // ✅ Usar 'issues' en lugar de 'errors'
    });
  }

  // JWT/JOSE errors
  if (error.name === 'JWTExpired' || error.name === 'JWSSignatureVerificationFailed') {
    logger.warn({ error: error.message, reqId }, 'JWT error');
    return res.status(401).json({ error: 'invalid_token' });
  }

  // Database errors
  if (error.message?.includes('ER_')) {
    logger.error({ error: error.message, reqId }, 'Database error');
    return res.status(503).json({ error: 'database_unavailable' });
  }

  // Log unknown errors
  logger.error({ error, reqId, stack: error.stack }, 'Unhandled error');

  // Never expose internal errors in production
  const isDev = process.env.NODE_ENV === 'development';
  res.status(500).json({
    error: 'internal_server_error',
    ...(isDev && { detail: error.message })
  });
}

// Wrapper para async route handlers
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
