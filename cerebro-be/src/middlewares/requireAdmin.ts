import { Request, Response, NextFunction } from 'express';
import { parseBearerFromReq, verifyAccessToken } from '../services/tokens.js';
import { userHasRole } from '../services/users.js';

// Protege rutas admin con Access Token válido + rol ADMIN
export default async function requireAdmin(req: Request, res: Response, next: NextFunction) {
  try {
    const token = parseBearerFromReq(req);
    if (!token) return res.status(401).json({ error: 'missing_token' });

    const { payload } = await verifyAccessToken(token);
    const sub = payload.sub;
    if (!sub) return res.status(401).json({ error: 'invalid_token' });

    const isAdmin = await userHasRole(Number(sub), 'ADMIN');
    if (!isAdmin) return res.status(403).json({ error: 'forbidden' });

    (req as any).auth = payload;
    next();
  } catch (e: any) {
    return res.status(401).json({ error: 'unauthorized', detail: e?.message });
  }
}
