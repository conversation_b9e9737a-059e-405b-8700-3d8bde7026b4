// src/middlewares/requireAuth.ts - versión corregida
import { Request, Response, NextFunction } from 'express';
import { parseBearerFromReq, verifyAccessToken, AccessTokenPayload } from '../services/tokens.js';
import { getUserRoles } from '../services/users.js';

// Extender Request para incluir auth
declare global {
  namespace Express {
    interface Request {
      auth?: AccessTokenPayload;
      userRoles?: string[];
    }
  }
}

export const requireAuth = (options: {
  scopes?: string[];
  roles?: string[];
  features?: string[];
} = {}) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const token = parseBearerFromReq(req);
      if (!token) {
        return res.status(401).json({ error: 'missing_token' });
      }

      const { payload } = await verifyAccessToken(token);
      req.auth = payload as AccessTokenPayload;

      // ✅ Verificar scopes si se requieren - corregido
      if (options.scopes?.length) {
        const scopeString = payload.scope;
        const tokenScopes = typeof scopeString === 'string' ? scopeString.split(' ') : [];
        const hasRequiredScope = options.scopes.some(scope => tokenScopes.includes(scope));
        if (!hasRequiredScope) {
          return res.status(403).json({ error: 'insufficient_scope' });
        }
      }

      // Verificar roles si se requieren
      if (options.roles?.length) {
        const userId = Number(payload.sub);
        const userRoles = await getUserRoles(userId);
        req.userRoles = userRoles;

        const hasRequiredRole = options.roles.some(role => userRoles.includes(role));
        if (!hasRequiredRole) {
          return res.status(403).json({ error: 'insufficient_role' });
        }
      }

      // TODO: Verificar features cuando implementes esa lógica

      next();
    } catch (error: any) {
      return res.status(401).json({
        error: 'unauthorized',
        detail: error?.code || error?.message
      });
    }
  };
};

// Shortcuts específicos
export const requireAdmin = requireAuth({ roles: ['ADMIN'] });
export const requireUser = requireAuth({ roles: ['ADMIN', 'CLIENT', 'PRO'] });
