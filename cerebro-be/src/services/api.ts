// src/services/api.ts
const API_BASE = 'http://localhost:4000';

export const authAPI = {
  login: (email: string, password: string) =>
    fetch(`${API_BASE}/admin/login`, { method: 'POST' }),

  getUsers: (token: string) =>
    fetch(`${API_BASE}/admin/users`, {
      headers: { Authorization: `Bearer ${token}` }
    }),

  // En tu backend, agregar rutas como:
  // GET /admin/users - listar usuarios
  // POST /admin/users - crear usuario
  // PUT /admin/users/:id - actualizar usuario
  // DELETE /admin/users/:id - eliminar usuario
  // GET /admin/clients - listar clientes OIDC
  // etc.
};
