// src/services/auth.ts
import { pool } from '../db.js';
import argon2 from 'argon2';
import { SignJWT, jwtVerify, JWTPayload } from 'jose';
import { randomUUID } from 'crypto';
// Si ya tienes utilidades de usuario, úsalas; si no, dejamos consultas directas:
import { getUserRoles, getUserById } from './users.js'; // ajusta si tu fichero está en otra ruta

// -----------------------------
// Config (env + defaults)
// -----------------------------
const ISSUER = process.env.OIDC_ISSUER ?? 'http://localhost:4000';
const AUDIENCE = process.env.OIDC_AUDIENCE ?? 'cerebro';
const ACCESS_TOKEN_TTL = Number(process.env.ACCESS_TOKEN_TTL ?? 3600); // 1h
const REFRESH_TOKEN_TTL = Number(process.env.REFRESH_TOKEN_TTL ?? 60 * 60 * 24 * 30); // 30d

// ¡Cámbialo en producción!
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET ?? 'dev-secret-change-me');

// Almacenamiento en memoria de refresh tokens (usa BD en prod)
const validRefreshTokens = new Set<string>();

// -----------------------------
// Tipos
// -----------------------------
export type LoginInput = { email: string; password: string; otp?: string };
export type LoginOutput = {
  access_token: string;
  refresh_token: string;
  token_type: 'Bearer';
  expires_in: number; // segundos
  user: { id: number; email: string; roles: string[] };
};

export type RefreshInput = { refreshToken: string };
export type RefreshOutput = Omit<LoginOutput, 'user'>;

export type LogoutInput = { sub?: string; refreshToken?: string };

export type ChangePasswordInput = { sub: string; currentPassword: string; newPassword: string };

export type ImpersonateInput = { targetUserId: number; actorSub?: string };
export type ImpersonateOutput = Omit<LoginOutput, 'user'> & {
  user: { id: number; email: string; roles: string[] };
};

// -----------------------------
// Helpers JWT
// -----------------------------
async function issueAccessToken(payload: {
  sub: string; email: string; roles: string[];
}) {
  const now = Math.floor(Date.now() / 1000);
  return await new SignJWT({ email: payload.email, roles: payload.roles })
    .setProtectedHeader({ alg: 'HS256', typ: 'JWT' })
    .setIssuer(ISSUER)
    .setAudience(AUDIENCE)
    .setSubject(payload.sub)
    .setIssuedAt(now)
    .setExpirationTime(now + ACCESS_TOKEN_TTL)
    .sign(JWT_SECRET);
}

async function issueRefreshToken(payload: { sub: string; type: 'refresh'; jti: string }) {
  const now = Math.floor(Date.now() / 1000);
  const token = await new SignJWT({ type: payload.type, jti: payload.jti })
    .setProtectedHeader({ alg: 'HS256', typ: 'JWT' })
    .setIssuer(ISSUER)
    .setAudience(AUDIENCE)
    .setSubject(payload.sub)
    .setIssuedAt(now)
    .setExpirationTime(now + REFRESH_TOKEN_TTL)
    .sign(JWT_SECRET);

  validRefreshTokens.add(token);
  return token;
}

async function verifyRefreshToken(token: string): Promise<JWTPayload & { sub: string }> {
  const { payload } = await jwtVerify(token, JWT_SECRET, { issuer: ISSUER, audience: AUDIENCE });
  if (!payload.sub) throw new Error('invalid_token_sub');
  if (!validRefreshTokens.has(token)) throw new Error('refresh_token_revoked');
  if (payload.type !== 'refresh') throw new Error('invalid_refresh_token');
  return payload as JWTPayload & { sub: string };
}

// -----------------------------
// Helpers DB
// -----------------------------
async function findUserByEmail(email: string): Promise<{ id: number; email: string; password_hash: string } | null> {
  const [[row]]: any[] = await pool.query(
    'SELECT id, email, password_hash FROM users WHERE email = ? LIMIT 1',
    [email]
  );
  return row ?? null;
}

// -----------------------------
// Servicio
// -----------------------------
export const authService = {
  async login({ email, password, otp }: LoginInput): Promise<LoginOutput> {
    // 1) Buscar usuario
    const user = await findUserByEmail(email);
    if (!user) throw new Error('invalid_credentials');

    // 2) Verificar password
    const ok = await argon2.verify(user.password_hash, password);
    if (!ok) throw new Error('invalid_credentials');

    // 3) (Opcional) Verificar OTP si lo usas realmente
    if (otp !== undefined) {
      // TODO: valida tu TOTP/HOTP aquí
      // if (!isValidOtp(user.id, otp)) throw new Error('invalid_otp');
    }

    // 4) Roles
    const roles = await getUserRoles(user.id);

    // 5) Emitir tokens
    const sub = String(user.id);
    const access_token = await issueAccessToken({ sub, email: user.email, roles });
    const refresh_token = await issueRefreshToken({ sub, type: 'refresh', jti: randomUUID() });

    return {
      access_token,
      refresh_token,
      token_type: 'Bearer',
      expires_in: ACCESS_TOKEN_TTL,
      user: { id: user.id, email: user.email, roles },
    };
  },

  async refresh({ refreshToken }: RefreshInput): Promise<RefreshOutput> {
    const payload = await verifyRefreshToken(refreshToken);
    const userId = Number(payload.sub);
    if (!Number.isFinite(userId)) throw new Error('invalid_sub');

    const user = await getUserById(userId);
    if (!user) throw new Error('user_not_found');

    const roles = await getUserRoles(userId);
    const access_token = await issueAccessToken({ sub: String(userId), email: user.email, roles });
    // (Opcional) rotación de refresh token:
    validRefreshTokens.delete(refreshToken);
    const new_refresh = await issueRefreshToken({ sub: String(userId), type: 'refresh', jti: randomUUID() });

    return {
      access_token,
      refresh_token: new_refresh,
      token_type: 'Bearer',
      expires_in: ACCESS_TOKEN_TTL,
    };
  },

  async logout({ sub, refreshToken }: LogoutInput): Promise<void> {
    if (refreshToken) validRefreshTokens.delete(refreshToken);
    // Si quieres invalidar todos los refresh del usuario, en prod hazlo por BD (revocar por sub/jti).
  },

  async changePassword({ sub, currentPassword, newPassword }: ChangePasswordInput): Promise<void> {
    const userId = Number(sub);
    if (!Number.isFinite(userId)) throw new Error('invalid_sub');

    const [[user]]: any[] = await pool.query(
      'SELECT id, password_hash FROM users WHERE id = ? LIMIT 1',
      [userId]
    );
    if (!user) throw new Error('user_not_found');

    const ok = await argon2.verify(user.password_hash, currentPassword);
    if (!ok) throw new Error('invalid_current_password');

    const newHash = await argon2.hash(newPassword);
    await pool.query('UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?', [newHash, userId]);

    // (Opcional) Revoca refresh tokens existentes del usuario (en prod, por BD).
  },

  async impersonate({ targetUserId, actorSub }: ImpersonateInput): Promise<ImpersonateOutput> {
    // Puedes auditar aquí quién impersona a quién con actorSub
    const target = await getUserById(targetUserId);
    if (!target) throw new Error('target_user_not_found');

    const roles = await getUserRoles(targetUserId);
    const sub = String(targetUserId);

    const access_token = await issueAccessToken({ sub, email: target.email, roles });
    const refresh_token = await issueRefreshToken({ sub, type: 'refresh', jti: randomUUID() });

    return {
      access_token,
      refresh_token,
      token_type: 'Bearer',
      expires_in: ACCESS_TOKEN_TTL,
      user: { id: targetUserId, email: target.email, roles },
    };
  },
};
