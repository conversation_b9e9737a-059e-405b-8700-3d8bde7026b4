import { pool } from '../db.js';
import argon2 from 'argon2';

export type User = {
  id: number;
  email: string;
  password_hash: string;
  display_name: string | null;
};

export async function getUserByEmail(email: string): Promise<User | null> {
  const [rows]: any[] = await pool.query(
    'SELECT * FROM users WHERE email=? LIMIT 1',
    [email]
  );
  return rows[0] ?? null;
}

export async function getUserById(id: number): Promise<User | null> {
  const [rows]: any[] = await pool.query(
    'SELECT * FROM users WHERE id=? LIMIT 1',
    [id]
  );
  return rows[0] ?? null;
}

export async function verifyPassword(user: User, password: string): Promise<boolean> {
  return argon2.verify(user.password_hash, password);
}

export async function getUserRoles(userId: number): Promise<string[]> {
  const [rows]: any[] = await pool.query(
    `SELECT r.name
     FROM user_roles ur
     JOIN roles r ON r.id = ur.role_id
     WHERE ur.user_id=?`,
    [userId]
  );
  return rows.map((r: any) => r.name);
}

export async function userHasRole(userId: number, roleName: string): Promise<boolean> {
  const roles = await getUserRoles(userId);
  return roles.includes(roleName);
}
