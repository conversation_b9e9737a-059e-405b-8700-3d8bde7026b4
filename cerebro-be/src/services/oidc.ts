import { pool } from '../db.js';

export type OidcClient = {
  id: number;
  client_id: string;
  client_secret: string | null;
  redirect_uris: string; // JSON array
  name: string | null;
  allowed_scopes: string | null;
  require_pkce: number; // tinyint(1)
};

export async function getClientById(clientId: string): Promise<OidcClient | null> {
  const [rows]: any[] = await pool.query(
    'SELECT * FROM oidc_clients WHERE client_id=? LIMIT 1',
    [clientId]
  );
  return rows[0] ?? null;
}

export function isRedirectUriAllowed(client: OidcClient, redirectUri: string): boolean {
  try {
    const allowed = JSON.parse(client.redirect_uris) as string[];
    return allowed.includes(redirectUri);
  } catch {
    return false;
  }
}
