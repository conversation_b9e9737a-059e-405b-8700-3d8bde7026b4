import { Request } from 'express';
import { createLocalJWKSet, jwtVerify, JWTPayload } from 'jose';
import { pool } from '../db.js';
import { env } from '../env.js';

export function parseBearerFromReq(req: Request): string | null {
  const auth = req.headers.authorization || '';
  if (!auth.startsWith('Bearer ')) return null;
  return auth.slice(7);
}

export async function getActiveJWKS() {
  const [rows]: any[] = await pool.query(
    'SELECT kid, public_jwk FROM jwk_keys WHERE is_active=1 LIMIT 1'
  );
  if (!rows.length) throw new Error('no_active_key');
  const row = rows[0];
  const jwk = JSON.parse(row.public_jwk);
  jwk.kid = row.kid;
  return createLocalJWKSet({ keys: [jwk] });
}

export async function verifyAccessToken(token: string) {
  const JWKS = await getActiveJWKS();
  // aud del access_token = env.OIDC_AUDIENCE (según nuestro diseño)
  return await jwtVerify(token, JWKS, {
    issuer: env.OIDC_ISSUER,
    audience: env.OIDC_AUDIENCE,
  });
}

export type AccessTokenPayload = JWTPayload & {
  sub: string;
  scope?: string;
};
