// src/routes/admin.ts
import { Router, Request, Response, NextFunction, RequestHandler } from 'express';
import { ParsedQs } from 'qs';
import { z } from 'zod';
import { pool } from '../db.js';
import { requireAdmin } from '../middlewares/requireAuth.js';
import { getUserRoles, getUserById } from '../services/users.js';
import { getClientById } from '../services/oidc.js';
import argon2 from 'argon2';

// ----------------------
// Tipado base
// ----------------------
export type Req<P = Record<string, string>, B = any, Q = ParsedQs> =
  Request<P, any, B, Q>;

// Handlers que SIEMPRE devuelven void (no Response)
type Handler<P = Record<string, string>, B = any, Q = ParsedQs> =
  (req: Req<P, B, Q>, res: Response, next: NextFunction) => void | Promise<void>;

// Wrapper async que atrapa errores y mantiene el tipo void / Promise<void>
const aw = <P, ResBody, ReqBody, ReqQuery>(
  fn: (req: Request<P, ResBody, ReqBody, ReqQuery>, res: Response<ResBody>, next: NextFunction) => void | Promise<void>
): RequestHandler<P, ResBody, ReqBody, ReqQuery> => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// Si tu app añade req.auth o req.userRoles:
type ReqAuthExtras = {
  auth?: { sub?: string };
  userRoles?: string[];
};
type ReqWithAuth<P = Record<string, string>, B = any, Q = ParsedQs> =
  Req<P, B, Q> & ReqAuthExtras;

// ----------------------
// Schemas Zod
// ----------------------
const PaginationQuery = z.object({
  page: z.string().regex(/^\d+$/).transform(Number).optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  search: z.string().trim().optional(),
});

const CreateUserSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  display_name: z.string().optional(),
  roles: z.array(z.string()).default(['CLIENT']),
});

const UpdateUserSchema = z.object({
  email: z.string().email().optional(),
  password: z.string().min(8).optional(),
  display_name: z.string().optional(),
  roles: z.array(z.string()).optional(),
});

const CreateClientSchema = z.object({
  client_id: z.string().min(1),
  name: z.string().min(1),
  redirect_uris: z.array(z.string().url()).min(1),
  allowed_scopes: z.string().min(1),
  require_pkce: z.boolean().default(true),
});

const UpdateClientSchema = z.object({
  name: z.string().min(1).optional(),
  redirect_uris: z.array(z.string().url()).min(1).optional(),
  allowed_scopes: z.string().min(1).optional(),
  require_pkce: z.boolean().optional(),
});

// ----------------------
// Router
// ----------------------
const r = Router();
r.use(requireAdmin);

// ---------- USERS ----------

// GET /admin/users
r.get(
  '/users',
  aw(async (req: Req<Record<string, never>, never, z.infer<typeof PaginationQuery>>, res) => {
    const parsed = PaginationQuery.safeParse(req.query);
    if (!parsed.success) {
      res.status(400).json({ error: 'validation_error', details: parsed.error.issues });
      return;
    }
    const { page = 1, limit = 10, search = '' } = parsed.data;
    const offset = (page - 1) * limit;

    let whereClause = '';
    let params: any[] = [];
    if (search) {
      whereClause = 'WHERE email LIKE ? OR display_name LIKE ?';
      params = [`%${search}%`, `%${search}%`];
    }

    const [users]: any[] = await pool.query(
      `SELECT id, email, display_name, created_at, updated_at
       FROM users ${whereClause}
       ORDER BY created_at DESC
       LIMIT ? OFFSET ?`,
      [...params, limit, offset]
    );

    const [[countResult]]: any[] = await pool.query(
      `SELECT COUNT(*) as total FROM users ${whereClause}`,
      params
    );

    const usersWithRoles = await Promise.all(
      (users as any[]).map(async (user: any) => {
        const roles = await getUserRoles(user.id);
        return { ...user, roles };
      })
    );

    res.json({
      users: usersWithRoles,
      pagination: {
        page,
        limit,
        total: countResult.total,
        pages: Math.ceil(countResult.total / limit),
      },
    });
  })
);

// GET /admin/users/:id
r.get(
  '/users/:id',
  aw(async (req: Req<{ id: string }>, res) => {
    const userId = Number(req.params.id);
    if (!Number.isFinite(userId)) {
      res.status(400).json({ error: 'invalid_id' });
      return;
    }

    const user = await getUserById(userId);
    if (!user) {
      res.status(404).json({ error: 'user_not_found' });
      return;
    }

    const roles = await getUserRoles(userId);
    res.json({ ...user, password_hash: undefined, roles });
  })
);

// POST /admin/users
r.post(
  '/users',
  aw(async (req: Req<Record<string, string>, z.infer<typeof CreateUserSchema>>, res) => {
    const parsed = CreateUserSchema.safeParse(req.body);
    if (!parsed.success) {
      res.status(400).json({ error: 'validation_error', details: parsed.error.issues });
      return;
    }
    const { email, password, display_name, roles } = parsed.data;

    const [[existingUser]]: any[] = await pool.query(
      'SELECT id FROM users WHERE email = ? LIMIT 1',
      [email]
    );
    if (existingUser) {
      res.status(409).json({ error: 'email_already_exists' });
      return;
    }

    const password_hash = await argon2.hash(password);
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      const [result]: any = await connection.query(
        'INSERT INTO users (email, password_hash, display_name) VALUES (?, ?, ?)',
        [email, password_hash, display_name ?? null]
      );
      const userId: number = result.insertId;

      if (roles?.length) {
        for (const roleName of roles) {
          await connection.query(
            'INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)',
            [userId, roleName]
          );
        }
      }

      await connection.commit();

      const created = await getUserById(userId);
      if (!created) {
        res.status(500).json({ error: 'created_but_not_found' });
        return;
      }

      const createdRoles = await getUserRoles(userId);
      res.status(201).json({ ...created, password_hash: undefined, roles: createdRoles });
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  })
);

// PUT /admin/users/:id
r.put(
  '/users/:id',
  aw(async (req: Req<{ id: string }, z.infer<typeof UpdateUserSchema>>, res) => {
    const parsed = UpdateUserSchema.safeParse(req.body);
    if (!parsed.success) {
      res.status(400).json({ error: 'validation_error', details: parsed.error.issues });
      return;
    }

    const userId = Number(req.params.id);
    if (!Number.isFinite(userId)) {
      res.status(400).json({ error: 'invalid_id' });
      return;
    }

    const { email, password, display_name, roles } = parsed.data;

    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      if (email !== undefined) {
        const [[dupe]]: any[] = await connection.query(
          'SELECT id FROM users WHERE email = ? AND id <> ? LIMIT 1',
          [email, userId]
        );
        if (dupe) {
          await connection.rollback();
          res.status(409).json({ error: 'email_already_exists' });
          return;
        }
      }

      const updates: string[] = [];
      const values: any[] = [];

      if (email !== undefined) { updates.push('email = ?'); values.push(email); }
      if (display_name !== undefined) { updates.push('display_name = ?'); values.push(display_name ?? null); }
      if (password !== undefined) { updates.push('password_hash = ?'); values.push(await argon2.hash(password)); }

      if (updates.length) {
        values.push(userId);
        await connection.query(
          `UPDATE users SET ${updates.join(', ')}, updated_at = NOW() WHERE id = ?`,
          values
        );
      }

      if (roles) {
        await connection.query('DELETE FROM user_roles WHERE user_id = ?', [userId]);
        for (const roleName of roles) {
          await connection.query(
            'INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)',
            [userId, roleName]
          );
        }
      }

      await connection.commit();

      const updated = await getUserById(userId);
      if (!updated) {
        res.status(404).json({ error: 'user_not_found' });
        return;
      }
      const updatedRoles = await getUserRoles(userId);
      res.json({ ...updated, password_hash: undefined, roles: updatedRoles });
    } catch (err) {
      await connection.rollback();
      throw err;
    } finally {
      connection.release();
    }
  })
);

// DELETE /admin/users/:id
r.delete(
  '/users/:id',
  aw(async (req: Req<{ id: string }>, res) => {
    const userId = Number(req.params.id);
    if (!Number.isFinite(userId)) {
      res.status(400).json({ error: 'invalid_id' });
      return;
    }

    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();
      await connection.query('DELETE FROM user_roles WHERE user_id = ?', [userId]);
      const [result]: any = await connection.query('DELETE FROM users WHERE id = ?', [userId]);
      await connection.commit();

      if (result.affectedRows === 0) {
        res.status(404).json({ error: 'user_not_found' });
        return;
      }
      res.sendStatus(204);
    } catch (e) {
      await connection.rollback();
      throw e;
    } finally {
      connection.release();
    }
  })
);

// ---------- ROLES ----------

r.get(
  '/roles',
  aw(async (_req: Req, res) => {
    const [roles]: any[] = await pool.query('SELECT id, name FROM roles ORDER BY name');
    res.json({ roles });
  })
);

// ---------- OIDC CLIENTS ----------

// GET /admin/clients
r.get(
  '/clients',
  aw(async (_req: Req, res) => {
    const [clients]: any[] = await pool.query(
      `SELECT id, client_id, name, redirect_uris, allowed_scopes,
              require_pkce, created_at
       FROM oidc_clients
       ORDER BY created_at DESC`
    );
    const parsedClients = (clients as any[]).map((client: any) => ({
      ...client,
      redirect_uris: JSON.parse(client.redirect_uris),
      require_pkce: Boolean(client.require_pkce),
    }));
    res.json({ clients: parsedClients });
  })
);

// GET /admin/clients/:id
r.get(
  '/clients/:id',
  aw(async (req: Req<{ id: string }>, res) => {
    const idNum = Number(req.params.id);
    if (!Number.isFinite(idNum)) {
      res.status(400).json({ error: 'invalid_id' });
      return;
    }

    const client = await getClientById(String(idNum)); // <- espera string
    if (!client) {
      res.status(404).json({ error: 'client_not_found' });
      return;
    }

    const normalized = {
      ...client,
      redirect_uris: Array.isArray((client as any).redirect_uris)
        ? (client as any).redirect_uris
        : JSON.parse(String((client as any).redirect_uris ?? '[]')),
      require_pkce: Boolean((client as any).require_pkce),
    };
    res.json(normalized);
  })
);

// POST /admin/clients
r.post(
  '/clients',
  aw(async (req: Req<Record<string, string>, z.infer<typeof CreateClientSchema>>, res) => {
    const parsed = CreateClientSchema.safeParse(req.body);
    if (!parsed.success) {
      res.status(400).json({ error: 'validation_error', details: parsed.error.issues });
      return;
    }
    const { client_id, name, redirect_uris, allowed_scopes, require_pkce } = parsed.data;

    const [[dupe]]: any[] = await pool.query(
      'SELECT id FROM oidc_clients WHERE client_id = ? LIMIT 1',
      [client_id]
    );
    if (dupe) {
      res.status(409).json({ error: 'client_id_exists' });
      return;
    }

    const [result]: any = await pool.query(
      `INSERT INTO oidc_clients (client_id, name, redirect_uris, allowed_scopes, require_pkce, created_at)
       VALUES (?, ?, ?, ?, ?, NOW())`,
      [client_id, name, JSON.stringify(redirect_uris), allowed_scopes, require_pkce ? 1 : 0]
    );

    const createdId: number = result.insertId;
    const created = await getClientById(String(createdId)); // <- string
    res.status(201).json(
      created ?? { id: createdId, client_id, name, redirect_uris, allowed_scopes, require_pkce }
    );
  })
);

// PUT /admin/clients/:id
r.put(
  '/clients/:id',
  aw(async (req: Req<{ id: string }, z.infer<typeof UpdateClientSchema>>, res) => {
    const idNum = Number(req.params.id);
    if (!Number.isFinite(idNum)) {
      res.status(400).json({ error: 'invalid_id' });
      return;
    }

    const parsed = UpdateClientSchema.safeParse(req.body);
    if (!parsed.success) {
      res.status(400).json({ error: 'validation_error', details: parsed.error.issues });
      return;
    }

    const { name, redirect_uris, allowed_scopes, require_pkce } = parsed.data;

    const updates: string[] = [];
    const values: any[] = [];

    if (name !== undefined) { updates.push('name = ?'); values.push(name); }
    if (redirect_uris !== undefined) { updates.push('redirect_uris = ?'); values.push(JSON.stringify(redirect_uris)); }
    if (allowed_scopes !== undefined) { updates.push('allowed_scopes = ?'); values.push(allowed_scopes); }
    if (require_pkce !== undefined) { updates.push('require_pkce = ?'); values.push(require_pkce ? 1 : 0); }

    if (!updates.length) {
      res.json({ updated: false });
      return;
    }

    values.push(idNum);
    const [result]: any = await pool.query(
      `UPDATE oidc_clients SET ${updates.join(', ')}, updated_at = NOW() WHERE id = ?`,
      values
    );

    if (result.affectedRows === 0) {
      res.status(404).json({ error: 'client_not_found' });
      return;
    }

    const updated = await getClientById(String(idNum)); // <- string
    res.json(updated ?? { id: idNum, name, redirect_uris, allowed_scopes, require_pkce });
  })
);

// DELETE /admin/clients/:id
r.delete(
  '/clients/:id',
  aw(async (req: Req<{ id: string }>, res) => {
    const idNum = Number(req.params.id);
    if (!Number.isFinite(idNum)) {
      res.status(400).json({ error: 'invalid_id' });
      return;
    }

    const [result]: any = await pool.query('DELETE FROM oidc_clients WHERE id = ?', [idNum]);
    if (result.affectedRows === 0) {
      res.status(404).json({ error: 'client_not_found' });
      return;
    }

    res.sendStatus(204);
  })
);

// ---------- STATS / CONFIG / JWK ----------

r.get(
  '/stats',
  aw(async (_req: Req, res) => {
    const [[userCount]]: any[] = await pool.query('SELECT COUNT(*) as count FROM users');
    const [[clientCount]]: any[] = await pool.query('SELECT COUNT(*) as count FROM oidc_clients');
    const [[tokenCount]]: any[] = await pool.query('SELECT COUNT(*) as count FROM oauth_tokens WHERE revoked = 0');
    const [[recentUsers]]: any[] = await pool.query(
      'SELECT COUNT(*) as count FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)'
    );

    res.json({
      stats: {
        total_users: userCount.count,
        total_clients: clientCount.count,
        active_tokens: tokenCount.count,
        recent_users: recentUsers.count,
      },
    });
  })
);

r.get(
  '/config',
  aw(async (_req: Req, res) => {
    const config = {
      oidc_issuer: process.env.OIDC_ISSUER,
      oidc_audience: process.env.OIDC_AUDIENCE,
      public_base_url: process.env.PUBLIC_BASE_URL,
    };
    res.json({ config });
  })
);

r.post(
  '/jwk/rotate',
  aw(async (_req: Req, res) => {
    // TODO: jwkService.rotateActiveKey();
    res.json({ ok: true, rotated: true });
  })
);

// ---------- HEALTHCHECK ----------

r.get(
  '/ping',
  aw(async (req: ReqWithAuth, res) => {
    res.json({
      ok: true,
      user: req.auth?.sub,
      roles: req.userRoles,
      timestamp: new Date().toISOString(),
    });
  })
);

export default r;
