// src/routes/wellKnown.ts
import { Router } from 'express';
import { env } from '../env.js';
const r = Router();

r.get('/openid-configuration', (req, res) => {
  const issuer = env.OIDC_ISSUER;
  res.json({
    issuer,
    authorization_endpoint: `${issuer}/authorize`,
    token_endpoint: `${issuer}/token`,
    userinfo_endpoint: `${issuer}/userinfo`,
    jwks_uri: `${issuer}/jwks.json`,
    response_types_supported: ['code'],
    grant_types_supported: ['authorization_code', 'refresh_token'],
    code_challenge_methods_supported: ['S256'],
    scopes_supported: ['openid', 'profile', 'email'],
    id_token_signing_alg_values_supported: ['RS256']
  });
});

export default r;
