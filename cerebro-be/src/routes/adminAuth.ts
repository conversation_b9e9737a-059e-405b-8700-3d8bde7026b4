// src/routes/adminAuth.ts - CORRECCIÓN

import { Router, Request, Response, NextFunction, RequestHandler } from 'express';
import { ParsedQs } from 'qs';
import { z } from 'zod';
import { requireAdmin, requireAuth } from '../middlewares/requireAuth.js';
import { authService, type LoginInput, type LogoutInput } from '../services/auth.js';

// ----------------------
// Tipado base
// ----------------------
export type Req<P = Record<string, string>, B = any, Q = ParsedQs> =
  Request<P, any, B, Q>;

type Handler<P = Record<string, string>, B = any, Q = ParsedQs> =
  (req: Req<P, B, Q>, res: Response, next: NextFunction) => void | Promise<void>;

// Wrapper async que mantiene tipo void/Promise<void> y pasa errores a next
const aw = <P, ResBody, ReqBody, ReqQuery>(
  fn: (req: Request<P, ResBody, ReqBody, ReqQuery>, res: Response<ResBody>, next: NextFunction) => void | Promise<void>
): RequestHandler<P, ResBody, ReqBody, ReqQuery> => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// Si tu app añade req.auth / req.user / req.userRoles:
type ReqAuthExtras = {
  auth?: { sub?: string; roles?: string[] };
  user?: { id: number; email: string; roles: string[] };
};
type ReqWithAuth<P = Record<string, string>, B = any, Q = ParsedQs> =
  Req<P, B, Q> & ReqAuthExtras;

// ----------------------
// Schemas Zod (ajusta a tu contrato real)
// ----------------------
const LoginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  otp: z.string().min(6).max(8).optional(),
});

const RefreshSchema = z.object({
  refresh_token: z.string().min(20),
});

const ChangePasswordSchema = z.object({
  current_password: z.string().min(8),
  new_password: z.string().min(8),
});

const ImpersonateSchema = z.object({
  user_id: z.number().int().positive(),
});

// ----------------------
// Router
// ----------------------
const r = Router();

/**
 * POST /admin/auth/login
 * Body: { email, password, otp? }
 * ✅ NO REQUIERE AUTENTICACIÓN - es el endpoint que GENERA el token
 */
r.post(
  '/login',
  aw(async (req: Req<Record<string, string>, z.infer<typeof LoginSchema>>, res) => {
    console.log('🔐 Admin login attempt:', req.body.email);

    const parsed = LoginSchema.safeParse(req.body);
    if (!parsed.success) {
      console.log('❌ Validation error:', parsed.error.issues);
      res.status(400).json({ error: 'validation_error', details: parsed.error.issues });
      return;
    }

    const { email, password, otp } = parsed.data;

    try {
      // *** CLAVE: no pases otp si es undefined ***
      let payload: LoginInput = { email, password };
      if (otp !== undefined) payload = { ...payload, otp };

      console.log('🔍 Attempting login for:', email);
      const result = await authService.login(payload);

      console.log('✅ Login successful for:', email);
      res.status(200).json(result);
    } catch (error: any) {
      console.log('❌ Login failed:', error.message);
      res.status(401).json({
        error: 'login_failed',
        message: error.message === 'invalid_credentials' ? 'Invalid email or password' : 'Login failed'
      });
    }
  })
);

/**
 * POST /admin/auth/refresh
 * Body: { refresh_token }
 * ✅ NO REQUIERE AUTENTICACIÓN - usa refresh token
 */
r.post(
  '/refresh', // ✅ SIN requireAuth middleware
  aw(async (req: Req<Record<string, string>, z.infer<typeof RefreshSchema>>, res) => {
    const parsed = RefreshSchema.safeParse(req.body);
    if (!parsed.success) {
      res.status(400).json({ error: 'validation_error', details: parsed.error.issues });
      return;
    }

    try {
      const { refresh_token } = parsed.data;
      const result = await authService.refresh({ refreshToken: refresh_token });
      res.json(result);
    } catch (error: any) {
      res.status(401).json({ error: 'refresh_failed', message: error.message });
    }
  })
);

/**
 * POST /admin/auth/logout
 * Body opcional: { refresh_token }
 * ✅ REQUIERE AUTENTICACIÓN
 */
r.post(
  '/logout',
  requireAuth, // ✅ Correcto: logout SÍ requiere auth
  aw(async (req: ReqWithAuth<Record<string, string>, { refresh_token?: string }>, res) => {
    const refreshToken = req.body?.refresh_token;

    // *** CLAVE: no pases sub/refreshToken si son undefined ***
    const payload: LogoutInput = {};
    if (req.auth?.sub) payload.sub = req.auth.sub;
    if (refreshToken) payload.refreshToken = refreshToken;

    await authService.logout(payload);
    res.sendStatus(204);
  })
);

/**
 * GET /admin/auth/me
 * ✅ REQUIERE AUTENTICACIÓN
 */
r.get(
  '/me',
  requireAuth, // ✅ Correcto: me SÍ requiere auth
  aw(async (req: ReqWithAuth, res) => {
    if (!req.user) {
      res.status(401).json({ error: 'unauthorized' });
      return;
    }
    res.json({ id: req.user.id, email: req.user.email, roles: req.user.roles });
  })
);

/**
 * POST /admin/auth/change-password
 * Body: { current_password, new_password }
 * ✅ REQUIERE AUTENTICACIÓN
 */
r.post(
  '/change-password',
  requireAuth, // ✅ Correcto: change password SÍ requiere auth
  aw(async (req: ReqWithAuth<Record<string, string>, z.infer<typeof ChangePasswordSchema>>, res) => {
    const parsed = ChangePasswordSchema.safeParse(req.body);
    if (!parsed.success) {
      res.status(400).json({ error: 'validation_error', details: parsed.error.issues });
      return;
    }

    if (!req.auth?.sub) {
      res.status(401).json({ error: 'unauthorized' });
      return;
    }

    const { current_password, new_password } = parsed.data;
    await authService.changePassword({ sub: req.auth.sub, currentPassword: current_password, newPassword: new_password });
    res.sendStatus(204);
  })
);

/**
 * POST /admin/auth/impersonate
 * Body: { user_id }  (solo ADMIN)
 * ✅ REQUIERE AUTENTICACIÓN DE ADMIN
 */
r.post(
  '/impersonate',
  requireAdmin, // ✅ Correcto: impersonate SÍ requiere admin auth
  aw(async (req: ReqWithAuth<Record<string, string>, z.infer<typeof ImpersonateSchema>>, res) => {
    const parsed = ImpersonateSchema.safeParse(req.body);
    if (!parsed.success) {
      res.status(400).json({ error: 'validation_error', details: parsed.error.issues });
      return;
    }

    const { user_id } = parsed.data;

    // *** CLAVE: actorSub solo si existe ***
    const tokens = await authService.impersonate({
      targetUserId: user_id,
      ...(req.auth?.sub && { actorSub: req.auth.sub }),
    });

    res.json(tokens);
  })
);

export default r;
