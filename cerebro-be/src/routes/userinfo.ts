// src/routes/userinfo.ts
import { Router } from 'express';
import { jwtVerify, createLocalJWKSet } from 'jose';
import { pool } from '../db.js';
import { requireAuth } from '../middlewares/requireAuth.js';

const r = Router();

async function getJwks() {
  const [rows]: any[] = await pool.query('SELECT kid, public_jwk FROM jwk_keys WHERE is_active=1 LIMIT 1');
  const row = rows[0];
  const jwk = JSON.parse(row.public_jwk); jwk.kid = row.kid;
  return createLocalJWKSet({ keys: [jwk] });
}

// r.get('/', async (req, res) => {
//   const auth = req.headers.authorization || '';
//   const token = auth.startsWith('Bearer ') ? auth.slice(7) : '';
//   if (!token) return res.status(401).json({ error: 'missing_token' });

//   const JWKS = await getJwks();
//   const { payload } = await jwtVerify(token, JWKS);
//   const sub = payload.sub!;
//   const [[user]]: any[] = await pool.query('SELECT id,email,display_name FROM users WHERE id=? LIMIT 1', [sub]);

//   res.json({
//     sub,
//     email: user?.email,
//     name: user?.display_name
//   });
// });

r.get('/', requireAuth({ scopes: ['openid'] }), async (req, res) => {
  const sub = req.auth!.sub;
  const [[user]]: any[] = await pool.query(
    'SELECT id,email,display_name FROM users WHERE id=? LIMIT 1',
    [sub]
  );

  res.json({
    sub,
    email: user?.email,
    name: user?.display_name
  });
});

export default r;
