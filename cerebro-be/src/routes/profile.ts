import { Router } from 'express';

const r = Router();

// GET /user/profile  (stub)
r.get('/profile', (req, res) => {
  // Más adelante: leer sub del access_token, cargar datos del usuario desde DB
  res.json({ ok: true, message: 'profile stub' });
});

// PUT /user/profile  (stub)
r.put('/profile', (req, res) => {
  // Más adelante: validación con zod + actualización en DB
  res.status(501).json({ error: 'not_implemented' });
});

export default r;
