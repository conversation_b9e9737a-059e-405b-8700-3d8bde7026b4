// src/routes/token.ts
import * as express from "express";
import { Router } from "express";
import { z } from "zod";
import { pool } from "../db.js";
import { env } from "../env.js";
import { createHash } from "crypto";
import crypto from "crypto";
import { signIdToken, ensureActiveJwk } from "../crypto/jwk.js";
import { asyncHandler } from '../middlewares/errorHandler.js';

const r = Router();
r.use((req, _res, next) => {
  req.headers["content-type"] = "application/x-www-form-urlencoded";
  next();
});

const Body = z.object({
  grant_type: z.enum(["authorization_code", "refresh_token"]),
  code: z.string().optional(),
  redirect_uri: z.string().url().optional(),
  client_id: z.string().optional(),
  code_verifier: z.string().optional(),
  refresh_token: z.string().optional(),
});

r.post("/", express.urlencoded({ extended: false }), async (req, res) => {
  const parsed = Body.safeParse(req.body);
  if (!parsed.success) return res.status(400).json(parsed.error);
  const body = parsed.data;

  if (body.grant_type === "authorization_code") {
    // validar code + PKCE
    const [[ac]]: any[] = await pool.query(
      "SELECT * FROM auth_codes WHERE code=? LIMIT 1",
      [body.code]
    );
    if (!ac) return res.status(400).json({ error: "invalid_grant" });
    if (new Date(ac.expires_at).getTime() < Date.now())
      return res.status(400).json({ error: "expired_code" });

    const cv = body.code_verifier!;
    const challenge = base64url(
      createHash("sha256").update(cv).digest("base64")
    );
    if (challenge !== ac.code_challenge)
      return res.status(400).json({ error: "pkce_check_failed" });

    // emitir tokens
    const kidRow = await ensureActiveJwk();
    const kid = kidRow.kid || kidRow.kid;
    const sub = String(ac.user_id);

    const id_token = await signIdToken(
      {
        iss: env.OIDC_ISSUER,
        aud: body.client_id,
        sub,
        email_verified: true,
      },
      kid
    );

    // access token simple (mismo JWK)
    const access_token = await signIdToken(
      { iss: env.OIDC_ISSUER, aud: env.OIDC_AUDIENCE, sub, scope: ac.scope },
      kid
    );

    // opcional: refresh token
    const refresh_token = crypto.randomUUID();
    const exp = new Date(Date.now() + 30 * 24 * 3600 * 1000);
    await pool.query(
      "INSERT INTO refresh_tokens (token,user_id,client_id,expires_at) VALUES (?,?,?,?)",
      [refresh_token, ac.user_id, ac.client_id, exp]
    );

    // usar y borrar code (single-use)
    await pool.query("DELETE FROM auth_codes WHERE code=?", [body.code]);

    return res.json({
      token_type: "Bearer",
      id_token,
      access_token,
      refresh_token,
      expires_in: 900,
    });
  }

  // refresh_token (MVP)
  if (body.grant_type === "refresh_token") {
    const { refresh_token, client_id } = body;

    if (!refresh_token || !client_id) {
      return res.status(400).json({ error: "missing_parameters" });
    }

    const [[rt]]: any[] = await pool.query(
      "SELECT * FROM refresh_tokens WHERE token=? AND revoked=0 LIMIT 1",
      [refresh_token]
    );

    if (!rt) {
      return res.status(400).json({ error: "invalid_refresh_token" });
    }

    // ✅ Validar que el refresh token pertenezca al cliente
    if (rt.client_id !== client_id) {
      console.warn("Client mismatch in refresh token", client_id, rt.client_id);
      return res.status(400).json({ error: "invalid_client" });
    }

    if (new Date(rt.expires_at).getTime() < Date.now()) {
      // Limpiar token expirado
      await pool.query("UPDATE refresh_tokens SET revoked=1 WHERE token=?", [
        refresh_token,
      ]);
      return res.status(400).json({ error: "expired_refresh_token" });
    }

    // ✅ Opcional: Rotar refresh token por seguridad
    const newRefreshToken = crypto.randomUUID();
    const newExp = new Date(Date.now() + 30 * 24 * 3600 * 1000);

    // Transacción para atomicidad
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      // Revocar el anterior
      await connection.query(
        "UPDATE refresh_tokens SET revoked=1 WHERE token=?",
        [refresh_token]
      );

      // Crear nuevo
      await connection.query(
        "INSERT INTO refresh_tokens (token,user_id,client_id,expires_at) VALUES (?,?,?,?)",
        [newRefreshToken, rt.user_id, rt.client_id, newExp]
      );

      await connection.commit();
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }

    const kidRow = await ensureActiveJwk();
    const sub = String(rt.user_id);
    const access_token = await signIdToken(
      {
        iss: env.OIDC_ISSUER,
        aud: env.OIDC_AUDIENCE,
        sub,
      },
      kidRow.kid
    );

    return res.json({
      token_type: "Bearer",
      access_token,
      refresh_token: newRefreshToken, // ✅ Nuevo refresh token
      expires_in: 900,
    });
  }

  return res.status(400).json({ error: "unsupported_grant_type" });
});

function base64url(b64: string) {
  return b64.replace(/=/g, "").replace(/\+/g, "-").replace(/\//g, "_");
}

export default r;
