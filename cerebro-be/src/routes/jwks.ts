import { Router } from 'express';
import { pool } from '../db.js';

const r = Router();

r.get('/', async (_, res) => {
  const [rows]: any[] = await pool.query(
    'SELECT kid, public_jwk FROM jwk_keys WHERE is_active=1 LIMIT 1'
  );
  if (!rows.length) return res.status(503).json({ error: 'no_active_key' });
  const { kid, public_jwk } = rows[0];
  const jwk = JSON.parse(public_jwk);
  jwk.kid = kid;
  res.json({ keys: [jwk] });
});

export default r;
