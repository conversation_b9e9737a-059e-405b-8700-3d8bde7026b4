// src/routes/adminAudit.ts
import { Router, Request, Response, NextFunction, RequestHandler } from 'express';
import { ParsedQs } from 'qs';
import { z } from 'zod';
import { pool } from '../db.js';
import { requireAdmin } from '../middlewares/requireAuth.js';

// ----------------------
// Tipado base
// ----------------------
export type Req<P = Record<string, string>, B = any, Q = ParsedQs> =
  Request<P, any, B, Q>;

// Handlers que no devuelven Response (void | Promise<void>)
type Handler<P = Record<string, string>, B = any, Q = ParsedQs> =
  (req: Req<P, B, Q>, res: Response, next: NextFunction) => void | Promise<void>;

// Wrapper async que mantiene el tipo void / Promise<void>
const aw = <P, ResBody, ReqBody, ReqQuery>(
  fn: (req: Request<P, ResBody, ReqBody, ReqQuery>, res: Response<ResBody>, next: NextFunction) => void | Promise<void>
): RequestHandler<P, ResBody, ReqBody, ReqQuery> => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// ----------------------
// Zod Schemas (filtros y utilidades)
// ----------------------

// Validar fecha ISO sin depender de .datetime() por compat:
const isoDate = z.string().refine(v => !Number.isNaN(Date.parse(v)), 'Invalid ISO date');

const ListQuery = z.object({
  page: z.string().regex(/^\d+$/).transform(Number).optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  action: z.string().trim().optional(),
  user_id: z.string().regex(/^\d+$/).transform(Number).optional(),
  resource_type: z.string().trim().optional(),
  resource_id: z.string().trim().optional(),
  ip: z.string().trim().optional(),
  from: isoDate.optional(),
  to: isoDate.optional(),
  sort: z.enum(['asc', 'desc']).optional(),
});

const CleanupBody = z.object({
  before: isoDate,                      // borrar logs anteriores a esta fecha
  actions: z.array(z.string()).optional(), // opcional limitar por acciones
});

type AuditLogRow = {
  id: number;
  timestamp: string;
  user_id: number | null;
  action: string;
  resource_type: string | null;
  resource_id: string | null;
  ip_address: string | null;
  user_agent: string | null;
  details: any | null;
};

// ----------------------
// Helpers SQL
// ----------------------
function buildWhere(filters: z.infer<typeof ListQuery>) {
  const clauses: string[] = [];
  const params: any[] = [];

  if (filters.action) { clauses.push('action = ?'); params.push(filters.action); }
  if (filters.user_id !== undefined) { clauses.push('user_id = ?'); params.push(filters.user_id); }
  if (filters.resource_type) { clauses.push('resource_type = ?'); params.push(filters.resource_type); }
  if (filters.resource_id) { clauses.push('resource_id = ?'); params.push(filters.resource_id); }
  if (filters.ip) { clauses.push('ip_address = ?'); params.push(filters.ip); }
  if (filters.from) { clauses.push('timestamp >= ?'); params.push(new Date(filters.from)); }
  if (filters.to) { clauses.push('timestamp <= ?'); params.push(new Date(filters.to)); }

  const where = clauses.length ? `WHERE ${clauses.join(' AND ')}` : '';
  return { where, params };
}

// ----------------------
// Router
// ----------------------
const r = Router();
r.use(requireAdmin);

/**
 * GET /admin/audit/logs
 * Lista de audit logs con filtros y paginación
 */
r.get(
  '/audit/logs',
  aw(async (req: Req<Record<string, never>, never, z.infer<typeof ListQuery>>, res) => {
    const parsed = ListQuery.safeParse(req.query);
    if (!parsed.success) {
      res.status(400).json({ error: 'validation_error', details: parsed.error.issues });
      return;
    }
    const { page = 1, limit = 20, sort = 'desc', ...filters } = parsed.data;
    const offset = (page - 1) * limit;

    const { where, params } = buildWhere(filters);

    const [rows]: any[] = await pool.query(
      `SELECT id, timestamp, user_id, action, resource_type, resource_id, ip_address, user_agent, details
       FROM audit_logs
       ${where}
       ORDER BY timestamp ${sort === 'asc' ? 'ASC' : 'DESC'}
       LIMIT ? OFFSET ?`,
      [...params, limit, offset]
    );

    // total
    const [[countRow]]: any[] = await pool.query(
      `SELECT COUNT(*) AS total FROM audit_logs ${where}`,
      params
    );

    // parsear JSON de details si viene como string
    const logs: AuditLogRow[] = (rows as any[]).map((r) => ({
      ...r,
      details: typeof r.details === 'string' ? safeParseJSON(r.details) : r.details,
      timestamp: new Date(r.timestamp).toISOString(),
    }));

    res.json({
      logs,
      pagination: {
        page,
        limit,
        total: countRow.total,
        pages: Math.ceil(countRow.total / limit),
      },
    });
  })
);

/**
 * GET /admin/audit/logs/:id
 * Detalle de un audit log
 */
r.get(
  '/audit/logs/:id',
  aw(async (req: Req<{ id: string }>, res) => {
    const idNum = Number(req.params.id);
    if (!Number.isFinite(idNum)) {
      res.status(400).json({ error: 'invalid_id' });
      return;
    }

    const [[row]]: any[] = await pool.query(
      `SELECT id, timestamp, user_id, action, resource_type, resource_id, ip_address, user_agent, details
       FROM audit_logs WHERE id = ? LIMIT 1`,
      [idNum]
    );

    if (!row) {
      res.status(404).json({ error: 'not_found' });
      return;
    }

    const log: AuditLogRow = {
      ...row,
      details: typeof row.details === 'string' ? safeParseJSON(row.details) : row.details,
      timestamp: new Date(row.timestamp).toISOString(),
    };

    res.json(log);
  })
);

/**
 * GET /admin/audit/actions
 * Lista de acciones distintas (para filtros en el panel)
 */
r.get(
  '/audit/actions',
  aw(async (_req: Req, res) => {
    const [rows]: any[] = await pool.query(
      `SELECT DISTINCT action FROM audit_logs ORDER BY action ASC`
    );
    res.json({ actions: (rows as any[]).map(r => r.action) });
  })
);

/**
 * GET /admin/audit/export.csv
 * Exportación CSV según los mismos filtros de /audit/logs
 */
r.get(
  '/audit/export.csv',
  aw(async (req: Req<Record<string, never>, never, z.infer<typeof ListQuery>>, res) => {
    const parsed = ListQuery.safeParse(req.query);
    if (!parsed.success) {
      res.status(400).json({ error: 'validation_error', details: parsed.error.issues });
      return;
    }
    const { sort = 'desc', ...filters } = parsed.data;
    const { where, params } = buildWhere(filters);

    const [rows]: any[] = await pool.query(
      `SELECT id, timestamp, user_id, action, resource_type, resource_id, ip_address, user_agent, details
       FROM audit_logs
       ${where}
       ORDER BY timestamp ${sort === 'asc' ? 'ASC' : 'DESC'}`,
      params
    );

    const header = [
      'id','timestamp','user_id','action','resource_type','resource_id','ip_address','user_agent','details'
    ];
    const lines = [header.join(',')];

    for (const r of rows as any[]) {
      const details = typeof r.details === 'string' ? r.details : JSON.stringify(r.details ?? null);
      lines.push([
        r.id,
        new Date(r.timestamp).toISOString(),
        r.user_id ?? '',
        csvEscape(r.action),
        csvEscape(r.resource_type),
        csvEscape(r.resource_id),
        csvEscape(r.ip_address),
        csvEscape(r.user_agent),
        csvEscape(details),
      ].join(','));
    }

    const csv = lines.join('\n');
    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename="audit_export_${Date.now()}.csv"`);
    res.send(csv);
  })
);

/**
 * DELETE /admin/audit/logs/:id
 * Borra un log concreto
 */
r.delete(
  '/audit/logs/:id',
  aw(async (req: Req<{ id: string }>, res) => {
    const idNum = Number(req.params.id);
    if (!Number.isFinite(idNum)) {
      res.status(400).json({ error: 'invalid_id' });
      return;
    }

    const [result]: any = await pool.query('DELETE FROM audit_logs WHERE id = ?', [idNum]);
    if (result.affectedRows === 0) {
      res.status(404).json({ error: 'not_found' });
      return;
    }
    res.sendStatus(204);
  })
);

/**
 * POST /admin/audit/cleanup
 * Borra logs anteriores a una fecha (y opcionalmente por acción)
 * Body: { before: ISODate, actions?: string[] }
 */
r.post(
  '/audit/cleanup',
  aw(async (req: Req<Record<string, string>, z.infer<typeof CleanupBody>>, res) => {
    const parsed = CleanupBody.safeParse(req.body);
    if (!parsed.success) {
      res.status(400).json({ error: 'validation_error', details: parsed.error.issues });
      return;
    }

    const before = new Date(parsed.data.before);
    const actions = parsed.data.actions;

    let sql = 'DELETE FROM audit_logs WHERE timestamp < ?';
    const params: any[] = [before];

    if (actions && actions.length) {
      sql += ` AND action IN (${actions.map(() => '?').join(',')})`;
      params.push(...actions);
    }

    const [result]: any = await pool.query(sql, params);
    res.json({ deleted: result.affectedRows ?? 0 });
  })
);

export default r;

// ----------------------
// Utils
// ----------------------
function safeParseJSON(text: string) {
  try { return JSON.parse(text); } catch { return text; }
}

function csvEscape(val: unknown): string {
  if (val === null || val === undefined) return '';
  const s = String(val);
  if (/[",\n]/.test(s)) return `"${s.replace(/"/g, '""')}"`;
  return s;
}
