// src/crypto/jwk.ts
import { generateKeyPair, exportJWK, SignJWT, importJWK } from 'jose';
import { randomUUID } from 'node:crypto';
import { pool } from '../db.js';

export async function ensureActiveJwk() {
  const [rows]: any[] = await pool.query('SELECT * FROM jwk_keys WHERE is_active=1 LIMIT 1');
  if (rows.length) return rows[0];

  // 👇 IMPORTANTE: extractable: true para poder exportar a JWK
  const { publicKey, privateKey } = await generateKeyPair('RS256', {
    modulusLength: 2048,
    extractable: true,
  });

  const publicJwk = await exportJWK(publicKey);
  const privateJwk = await exportJWK(privateKey);

  const kid = randomUUID();

  // OJO: usamos "key_use" (no "use") porque 'use' es palabra reservada en SQL
  await pool.query(
    'INSERT INTO jwk_keys (kid,kty,alg,key_use,public_jwk,private_jwk,is_active) VALUES (?,?,?,?,?,?,1)',
    [kid, publicJwk.kty, 'RS256', 'sig', JSON.stringify(publicJwk), JSON.stringify(privateJwk)]
  );

  return { kid, public_jwk: publicJwk, private_jwk: privateJwk, alg: 'RS256', key_use: 'sig' };
}

export async function signIdToken(payload: Record<string, any>, kid: string) {
  const [rows]: any[] = await pool.query('SELECT * FROM jwk_keys WHERE kid=? LIMIT 1', [kid]);
  const row = rows[0];
  const privateJwk = JSON.parse(row.private_jwk);
  const key = await importJWK(privateJwk, 'RS256');
  return await new SignJWT(payload)
    .setProtectedHeader({ alg: 'RS256', kid })
    .setIssuedAt()
    .setExpirationTime('15m')
    .sign(key);
}
