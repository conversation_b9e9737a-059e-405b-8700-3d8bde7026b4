# 🚀 Quick Start - Cerebro IDP

## 1. ⚡ Setup rápido (1 minuto)

```bash
# Clonar e instalar
git clone <repo-url>
cd cerebro-idp
npm install

# Setup automático completo
make dev-full
```

## 2. 🔧 Setup manual (si prefieres control)

```bash
# 1. Configurar entorno
make setup-env
# Editar .env con tus valores

# 2. Levantar servicios
make docker-up

# 3. Configurar base de datos
make db-setup

# 4. Arrancar desarrollo
make dev
```

## 3. ✅ Verificar instalación

```bash
# Health check
make health

# Ver endpoints principales
curl http://localhost:4000/.well-known/openid-configuration
curl http://localhost:4000/jwks.json
curl http://localhost:4000/health
```

## 4. 🎯 Usuarios por defecto

| Usuario | Email | Password | Rol |
|---------|-------|----------|-----|
| Admin | `<EMAIL>` | `SuperSegura123!` | ADMIN |
| Test User | `<EMAIL>` | `test123456` | CLIENT |

## 5. 🌐 URLs importantes

- **API**: http://localhost:4000
- **Health**: http://localhost:4000/health
- **OIDC Config**: http://localhost:4000/.well-known/openid-configuration
- **JWK**: http://localhost:4000/jwks.json
- **Adminer** (DB): http://localhost:8080

## 6. 🐛 Problemas comunes

**Error: Database connection failed**
```bash
make docker-up
make db-status
```

**Error: No active JWK found**
```bash
make gen-jwk
```

**Error: Port 4000 already in use**
```bash
# Cambiar PORT en .env
echo "PORT=4001" >> .env
```

## 7. 📝 Siguientes pasos

1. **Cambiar contraseñas**: Edita `ADMIN_PWD` y `JWT_SECRET` en `.env`
2. **Configurar CORS**: Ajusta `CORS_ORIGINS` para tu frontend
3. **Crear clientes OIDC**: Usa el panel admin o API
4. **Integrar con tu app**: Ver ejemplos en README.md

## 8. 🆘 Ayuda

```bash
make help           # Ver todos los comandos
make troubleshoot   # Diagnóstico automático
make health         # Health check
```

¡Listo para desarrollar! 🎉
