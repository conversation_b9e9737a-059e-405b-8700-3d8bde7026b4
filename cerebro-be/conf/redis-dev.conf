# redis-dev.conf - Configuración Redis para desarrollo
# Más permisiva y con más logging para debugging

# ================================================
# NETWORK
# ================================================
bind 0.0.0.0
port 6379
tcp-backlog 511
timeout 0
tcp-keepalive 300

# ================================================
# GENERAL
# ================================================
daemonize no
pidfile /var/run/redis_6379.pid
loglevel debug
logfile ""
databases 16

# ================================================
# MEMORY MANAGEMENT - Más relajado para desarrollo
# ================================================
maxmemory 128mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# ================================================
# PERSISTENCE - Más frecuente para no perder datos
# ================================================
# RDB Snapshots - Más frecuentes en desarrollo
save 300 1      # Save if at least 1 key changed in 300 seconds
save 60 10      # Save if at least 10 keys changed in 60 seconds
save 30 100     # Save if at least 100 keys changed in 30 seconds

stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# AOF - Habilitado para desarrollo para mejor persistencia
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 32mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# ================================================
# SECURITY - Más permisivo para desarrollo
# ================================================
# No password en desarrollo para facilitar debugging
# protected-mode no

# ================================================
# CLIENTS
# ================================================
maxclients 1000

# ================================================
# SLOW LOG - Más agresivo para debugging
# ================================================
slowlog-log-slower-than 1000  # 1ms (muy agresivo)
slowlog-max-len 1000

# ================================================
# LATENCY MONITOR
# ================================================
latency-monitor-threshold 10  # 10ms

# ================================================
# EVENT NOTIFICATION - Habilitado para debugging
# ================================================
notify-keyspace-events "KEA"

# ================================================
# DEVELOPMENT-SPECIFIC
# ================================================
# Habilitar comandos de debug
enable-debug-command yes

# Configuración más agresiva de limpieza
hz 100
dynamic-hz yes

# Timeouts más cortos para detectar problemas rápido
timeout 30

# ================================================
# LOGGING ADICIONAL PARA DESARROLLO
# ================================================
# Log todas las queries (útil para debugging)
# syslog-enabled yes
# syslog-ident redis-dev

# ================================================
# MONITOR COMMANDS (útil para debugging)
# ================================================
# En desarrollo, podemos monitorear comandos más fácilmente
# redis-cli monitor para ver todos los comandos en tiempo real
