# Redis configuration for Cerebro IDP
# Based on Redis 7.x

# ================================================
# NETWORK
# ================================================
bind 0.0.0.0
port 6379
tcp-backlog 511
timeout 300
tcp-keepalive 300

# ================================================
# GENERAL
# ================================================
daemonize no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16

# ================================================
# MEMORY MANAGEMENT
# ================================================
maxmemory 256mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# ================================================
# PERSISTENCE
# ================================================
# RDB Snapshots
save 900 1      # Save if at least 1 key changed in 900 seconds
save 300 10     # Save if at least 10 keys changed in 300 seconds
save 60 10000   # Save if at least 10000 keys changed in 60 seconds

stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# AOF (Append Only File) - Disabled by default for sessions
appendonly no
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# ================================================
# SECURITY
# ================================================
# requirepass your-strong-password-here
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""
# rename-command DEBUG ""
# rename-command CONFIG "CONFIG_b4f3e8c2a1"

# ================================================
# CLIENTS
# ================================================
maxclients 10000

# ================================================
# SLOW LOG
# ================================================
slowlog-log-slower-than 10000
slowlog-max-len 128

# ================================================
# LATENCY MONITOR
# ================================================
latency-monitor-threshold 100

# ================================================
# EVENT NOTIFICATION
# ================================================
notify-keyspace-events ""

# ================================================
# ADVANCED CONFIG
# ================================================
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000
stream-node-max-bytes 4096
stream-node-max-entries 100
activerehashing yes
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60
hz 10
dynamic-hz yes
aof-rewrite-incremental-fsync yes
rdb-save-incremental-fsync yes

# ================================================
# SESSION-SPECIFIC OPTIMIZATIONS
# ================================================
# Optimized for session storage and caching
tcp-keepalive 300
timeout 300

# Memory optimizations for sessions
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
