# .dockerignore - Optimizar builds Docker para Cerebro IDP

# ================================================
# Node.js
# ================================================
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn

# ================================================
# Build outputs
# ================================================
dist
dist-ssr
build
coverage
*.tsbuildinfo

# ================================================
# Logs
# ================================================
logs
*.log
pids
*.pid
*.seed
*.pid.lock

# ================================================
# Environment files
# ================================================
.env
.env.local
.env.development
.env.test
.env.production
.env.prod
.env.staging

# ================================================
# IDE and Editor files
# ================================================
.vscode
.idea
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# ================================================
# Git
# ================================================
.git
.gitignore
.gitattributes

# ================================================
# Documentation
# ================================================
README.md
CHANGELOG.md
LICENSE
*.md
docs/
_docs/

# ================================================
# Development files
# ================================================
.editorconfig
.eslintrc*
.prettierrc*
jest.config.*
*.config.js
*.config.ts
docker-compose*.yml
Dockerfile*
.dockerignore

# ================================================
# Testing
# ================================================
test/
tests/
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# ================================================
# Backups and temporary files
# ================================================
backups/
*.backup
*.bak
*.tmp
*.temp
.cache
.temp

# ================================================
# OS generated files
# ================================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ================================================
# Development tools
# ================================================
.nyc_output
coverage/
.sass-cache/
connect.lock
typings/
.grunt
bower_components
.lock-wscript
.eslintcache

# ================================================
# Deployment files
# ================================================
scripts/deploy.sh
scripts/ci-cd.sh
.github/
.gitlab-ci.yml
Makefile

# ================================================
# SSL Certificates (don't copy, mount instead)
# ================================================
ssl/
certs/
*.pem
*.key
*.crt
*.cert

# ================================================
# Database files (handled by volumes)
# ================================================
*.sql
*.db
*.sqlite
*.sqlite3

# ================================================
# Pero INCLUIR estos archivos necesarios:
# ================================================
# (Usamos ! para negar la exclusión)

# Mantener archivos de migración
!migrations/
!migrations/*.sql

# Mantener scripts necesarios para runtime
!scripts/health-check.ts
!scripts/migrate.ts
!scripts/seed.ts
!scripts/gen-jwk.ts
!scripts/cleanup.ts

# Mantener archivos de configuración esenciales
!package.json
!package-lock.json
!tsconfig.json

# Mantener código fuente
!src/
